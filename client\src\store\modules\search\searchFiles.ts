import { ThunkDispatch, UnknownAction } from '@reduxjs/toolkit';
import { FILES_SORT_DIRECTION, FILES_SORT_FIELD } from '@shared-types/types';
import HttpClient from '@store/dependencies/httpClient';
import { GQLApi } from '@utils/helpers';
import axios, { AxiosResponse } from 'axios';
import { cloneDeep, isEqual } from 'lodash';
import { forEach, forEachSeries, map } from 'p-iteration';
import { ArrayOrSingle, AsyncOrSync } from 'ts-essentials';
import {
  FaceDetectionTerm,
  FileSearchParams,
  QueryObjectSearchParams,
  ResultCategory,
  setFileSearchResult,
} from './slice';
import { MathOperators } from './constants';

/* eslint-disable no-param-reassign */

interface Condition {
  operator: string;
  field?: string;
  value?: string;
  values?: string[];
  not?: boolean;
  gte?: string;
  lte?: string;
  conditions?: Condition[];
}
interface Query {
  operator: string;
  field?: string;
  value?: string;
  values?: string[];
  conditions?: (Condition | Query)[];
}

const fileSearchVars: {
  search: {
    offset: number;
    limit: number;
    index: string[];
    type: string;
    query: Query;
    select: string[];
  };
} = {
  search: {
    offset: 0,
    limit: 10,
    index: ['mine'],
    type: 'file',
    query: {
      operator: 'and',
      conditions: [],
    },
    select: ['veritone-file'],
  },
};

interface SearchFilesSharedParams {
  params: FileSearchParams;
  lastParams?: FileSearchParams;
  gql: GQLApi;
  config: Window['config'];
  token: string;
  dispatch: ThunkDispatch<
    unknown,
    {
      http: HttpClient;
    },
    UnknownAction
  >;
  abortSignal?: AbortSignal;
  isPolling?: boolean;
  isCaseDetails?: boolean;
}

interface SearchConditions {
  caseIdConditions: Condition | undefined;
  caseStatusConditions: Condition | undefined;
  caseTagConditions: Condition | undefined;
  evidenceTypeFilterCondition: Condition | undefined;
  fileTypeFilterCondition:
    | {
        operator: string;
        conditions: { operator: string; field: string; value: string }[];
      }
    | undefined;
  uploadDateFilterCondition:
    | {
        operator: string;
        field: string;
        gte: string;
        lte: string;
      }
    | undefined;
  sortCondition: {
    field: FILES_SORT_FIELD;
    order: FILES_SORT_DIRECTION;
  }[];
  folderIdFilterCondition: Condition | undefined;
  noneSoftDeletedCondition: {
    operator: string;
    field: string;
    not: boolean;
    query: {
      operator: string;
      field: string;
      value: string;
      dotNotation: boolean;
    };
  };
}

const tinyId = () => Math.random().toString(36).substring(2, 15);

interface TreeNode {
  nodeType?: 'logic' | 'leaf';
  type:
    | 'foundFullTextString'
    | 'foundString'
    | 'operator'
    | 'entity'
    | 'library'
    | 'subtree'
    | 'string';
  operator?: string;
  entityId?: string;
  libraryId?: string;
  foundFullTextString?: string;
  foundString?: string;
  subTree?: TreeNode;
  id?: string;
  logic?: 'and' | 'or';
  children: TreeNode[];
  query?: Query;
}

interface ParentNode {
  children: TreeNode[];
  nodeType?: 'logic' | 'leaf';
  logic?: 'and' | 'or';
}

interface AutocompleteRequest {
  limit: number;
  index: string[];
  fields: string[];
  aggregateFields: string[];
  query: {
    operator: string;
    conditions: {
      operator: string;
      field: string;
      value: string;
    }[];
  };
}

export interface AutocompleteResponse {
  fields: {
    'object-recognition.series.found': {
      key: string;
      doc_count: number;
      docs: {
        entityId: string;
        entityName: string;
        libraryId: string;
        libraryName: string;
        libraryCoverImageUrl: null;
        profileImageUrl: string;
        created: number;
        modified: number;
        identifierType: string[];
      }[];
    }[];
    libraryName?: {
      key: string;
      doc_count: number;
      doc: {
        entityId: string;
        entityName: string;
        libraryId: string;
        libraryName: string;
        libraryCoverImageUrl: null | string;
        profileImageUrl: string;
        created: number;
        modified: number;
        identifierType: string[];
      };
    }[];
    entityName?: {
      key: string;
      doc_count: number;
      docs: {
        entityId: string;
        entityName: string;
        libraryId: string;
        libraryName: string;
        libraryCoverImageUrl: null;
        profileImageUrl: string;
        created: number;
        modified: number;
        identifierType: string[];
      }[];
      doc: {
        entityId: string;
        entityName: string;
        libraryId: string;
        libraryName: string;
        libraryCoverImageUrl: null;
        profileImageUrl: string;
        created: number;
        modified: number;
        identifierType: string[];
      };
    }[];
  };
}

export interface SearchMediaResponse {
  data?: SearchMediaResponse | undefined;
  searchMedia: {
    jsondata: {
      results: {
        recording: Recording;
        startDateTime: number;
        stopDateTime: number;
        context: {
          'veritone-job'?: Veritonejob;
          'veritone-file'?: Veritonefile;
          transcript?: {
            source: string;
            transcript: {
              startTime: number;
              endTime: number;
              text: string;
              words: string[];
            }[];
            version: string;
          };
          'face-recognition'?: {
            source?: string;
            series: {
              start: number;
              end: number;
              boundingPoly: BoundingPoly[];
              boundingBox: BoundingBox;
              libraryId: string;
              confidence: number;
            }[];
          };
        }[];
        hits: {
          'face-recognition'?: {
            series: {
              start: number;
              end: number;
              boundingPoly: BoundingPoly[];
              boundingBox: BoundingBox;
              libraryId: string;
              confidence: number;
            }[];
          };
          'text-recognition'?: {
            series: {
              start: number;
              end: number;
              boundingPoly: BoundingPoly[];
              ocrtext: string;
              hits: {
                queryTerm: string;
              }[];
            }[];
          };
          transcript?: {
            source: string;
            transcript: {
              startTime: number;
              endTime: number;
              text: string;
              words?: string[];
              hits: {
                queryTerm: string;
                startTime: number;
                endTime: number;
              }[];
            }[];
            version: string;
          };
        }[];
      }[];
      totalResults: {
        value: number;
        relation: string;
      };
      limit: number;
      from: number;
      to: number;
      searchToken: string;
      timestamp: number;
    };
  };
}

export interface BoundingBox {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
}

export interface BoundingPoly {
  x: number;
  y: number;
}

export interface Veritonefile {
  audionumchannels?: number;
  audiosamplerate?: number;
  duration?: number;
  filename?: string;
  sourceName?: string;
  hasaudio?: boolean;
  hasvideo?: boolean;
  height?: number;
  mimetype?: string;
  segmented?: boolean;
  videoframerate?: number;
  width?: number;
  size?: number;
  series?: Array<{
    start: number;
    end: number;
    boundingPoly: {
      x: number;
      y: number;
    }[];
    ocrtext: string;
    hits: {
      queryTerm: string;
    }[];
  }> | null;
}

export interface Veritonejob {
  engineCategories: string[];
  engines: string[];
}

export interface Recording {
  absoluteStartTimeMs: string;
  absoluteStopTimeMs: string;
  recordingId: string;
  fileLocation: string;
  fileType: string;
  programId: string;
  programName?: string;
  programLiveImage: string;
  mediaSourceId: string;
  mediaSourceTypeId: string;
  parentTreeObjectIds: string[];
  tags: Tag[];
  relativeStartTimeMs: number;
  relativeStopTimeMs: number;
  createdTime: string;
  modifiedTime: string;
  sliceTime: number;
  mediaStartTime: number;
  aibDuration: number;
  isOwn: boolean;
  hitStartTime: number;
  hitEndTime: number;
  caseId?: string;
  description?: string;
  creator: string;
  status: string;
  sourceName?: string;
}
interface Tag {
  displayName: string;
  value: string;
}

const removeNestedOrAndDuplicates = (condition: Condition | Query) => {
  const optimizedConditions: Condition[] = [];
  const seenConditions = new Set();

  const processConditions = (conditions: Condition[]) => {
    for (const condition of conditions) {
      if (condition.operator === 'or' && condition.conditions) {
        processConditions(condition.conditions);
      } else {
        const key = JSON.stringify(condition);
        if (!seenConditions.has(key)) {
          seenConditions.add(key);
          optimizedConditions.push(condition);
        }
      }
    }
  };

  if (condition && condition.conditions && condition.operator === 'or') {
    processConditions(condition.conditions);
  }

  return {
    ...condition,
    operator: condition.operator,
    ...(optimizedConditions.length > 0
      ? { conditions: optimizedConditions }
      : {}),
  };
};

export const optimizeQuery = (searchVars: typeof fileSearchVars) => {
  const query = searchVars.search.query;

  const buildTree = (input: Query) => {
    if (input.conditions) {
      const tree: Query = { operator: input.operator, conditions: [] };
      for (const condition of input.conditions) {
        const nestedTree = buildTree(removeNestedOrAndDuplicates(condition));
        if (nestedTree && tree.conditions) {
          tree.conditions.push(nestedTree);
        }
      }
      return tree;
    }
    return input;
  };

  const optimizedQuery = buildTree(query);
  return {
    ...searchVars,
    search: {
      ...searchVars.search,
      query: optimizedQuery,
    },
  };
};

const escapeBadChars = (text: string) =>
  text.replace(/[\+\-\=\&&\||\>\<\!\(\)\{\}\[\]\^\"\~\*\?\:\\\/]/g, '\\$&');

const buildTreeFromLogic = (logic: TreeNode[]): TreeNode | undefined => {
  const completedNonlogicNodes: string[] = [];

  logic = logic.map((l: TreeNode) => ({ ...l, id: tinyId() }));

  const parent: ParentNode = { children: [] };
  let isWrappedLogic = false;
  const hasParan =
    logic.find((l: TreeNode) => l.operator === '(') && logic.length > 0;

  if (logic[0]?.operator === '(' && hasParan) {
    let openParans = 1;

    for (let i = 1; i < logic.length; i++) {
      if (logic[i].operator === '(') {
        openParans++;
      }
      if (logic[i].operator === ')') {
        openParans--;
      }
      if (
        openParans === 0 &&
        logic[i].operator === ')' &&
        i !== logic.length - 1
      ) {
        isWrappedLogic = false;
        break;
      }
      if (logic.length - 1 === i && logic[i].operator === ')') {
        isWrappedLogic = true;
      }
    }
  }

  if (isWrappedLogic && hasParan) {
    return buildTreeFromLogic(logic.slice(1, logic.length - 1));
  }

  if (
    logic.length === 1 &&
    ['entity', 'library', 'foundString', 'foundFullTextString'].includes(
      logic[0].type
    )
  ) {
    const node = logic[0];

    if (node.type === 'foundString') {
      return {
        nodeType: 'leaf',
        type: node.type,
        query: {
          operator: node.foundString === '*' ? 'query_string' : 'term',
          field: 'object-recognition.series.found',
          value: node.foundString,
        },
        children: [],
      };
    } else if (node.type === 'foundFullTextString') {
      return {
        nodeType: 'leaf',
        type: node.type,
        query: {
          operator: 'query_string',
          field: 'object-recognition.series.found.fulltext',
          value: `*${node.foundFullTextString}*`,
        },
        children: [],
      };
    }

    return {
      nodeType: 'leaf',
      type: node.type,
      query: {
        operator:
          node.entityId === '*' || node.libraryId === '*'
            ? 'query_string'
            : 'term',
        field: `face-recognition.series.${node.type === 'entity' ? 'entityId' : 'libraryId'}`,
        value: node.type === 'entity' ? node.entityId : node.libraryId,
      },
      children: [],
    };
  }

  if (logic.length === 1 && logic[0].type === 'subtree') {
    return logic[0].subTree;
  }

  let done = false;

  for (const opType of ['()', 'or', 'and']) {
    if (done) {
      break;
    }
    for (let i = 0; i < logic.length; i++) {
      if (logic[i].type === 'operator') {
        const logicNode = logic[i];

        if (logicNode.operator === 'or' && logicNode.operator === opType) {
          parent.nodeType = 'logic';
          parent.logic = logicNode.operator;
          parent.children.push(buildTreeFromLogic(logic.slice(0, i))!);
          parent.children.push(buildTreeFromLogic(logic.slice(i + 1))!);
          done = true;
          break;
        }

        if (logicNode.operator === 'and' && logicNode.operator === opType) {
          parent.nodeType = 'logic';
          parent.logic = logicNode.operator;

          if (
            logic[i - 1] &&
            !completedNonlogicNodes.includes(logic[i - 1].id!)
          ) {
            if (logic[i - 1].type === 'subtree') {
              const st = logic[i - 1].subTree;
              if (st) {
                parent.children.push(st);
              }
            } else {
              parent.children.push(buildTreeFromLogic([logic[i - 1]])!);
            }
            if (logic[i - 1].id) {
              completedNonlogicNodes.push(logic[i - 1].id ?? '');
            }
          }
          if (
            logic[i + 1] &&
            !completedNonlogicNodes.includes(logic[i + 1].id!)
          ) {
            if (logic[i + 1].type === 'subtree') {
              const st = logic[i + 1].subTree;
              if (st) {
                parent.children.push(st);
              }
            } else {
              parent.children.push(buildTreeFromLogic([logic[i + 1]])!);
            }
            if (logic[i + 1].id) {
              completedNonlogicNodes.push(logic[i + 1].id ?? '');
            }
          }
          continue;
        }

        if (
          logicNode.operator === '(' &&
          opType.split('').includes(logicNode.operator)
        ) {
          let openParanCount = 1;
          const closingParanIndex = logic.findIndex(
            (l: TreeNode, index: number) => {
              if (l.operator === '(' && index > i) {
                openParanCount++;
              }
              if (l.operator === ')' && index > i) {
                openParanCount--;
              }

              return l.operator === ')' && index > i && openParanCount === 0;
            }
          );
          if (closingParanIndex === -1) {
            throw new Error('Unmatched parentheses');
          }
          const subLogic = logic.slice(i + 1, closingParanIndex);
          const subTree = buildTreeFromLogic(subLogic)!;
          logic = [
            ...logic.slice(0, i),
            { type: 'subtree', id: tinyId(), subTree, children: [] },
            ...logic.slice(closingParanIndex + 1),
          ];
          i = 0;
          continue;
        }
      }
    }
  }
  if (['and', 'or'].includes(parent?.logic ?? '')) {
    if (!parent.logic) {
      throw new Error('Logic node missing operator');
    }
    return {
      nodeType: 'logic',
      type: 'operator',
      query: {
        operator: parent.logic,
        conditions: parent.children.map((child) => {
          if (!child?.query) {
            throw new Error('There was a problem generating the query');
          }
          return child.query;
        }),
      },
      children: [],
    };
  }
};

export const queryEntitySearch = async ({
  gql,
  config,
  keyword,
  abortSignal,
  failOnNoLibraries = true,
}: {
  gql: GQLApi;
  config: Window['config'];
  keyword: string;
  abortSignal?: AbortSignal;
  failOnNoLibraries?: boolean;
}) => {
  const librariesResult = await gql?.getLibraries(abortSignal);
  const index = librariesResult?.libraries.records.map(
    (library) => `library:${library.id}`
  );
  if (!index || index.length === 0) {
    console.warn('Get libraries failed or none found');
    if (failOnNoLibraries) {
      throw new Error('No libraries found 1');
    } else {
      return;
    }
  }
  return await axios.post<
    AutocompleteRequest,
    AxiosResponse<AutocompleteResponse>
  >(
    `${config?.apiRoot ?? 'missing api root'}/api/search/autocomplete`,
    {
      limit: 10,
      index,
      fields: ['entityName'],
      aggregateFields: ['libraryId'],
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'term',
            field: 'identifierType',
            value: 'face',
          },
        ],
      },
      returnContext: true,
      text: keyword,
    },
    {
      signal: abortSignal,
      headers: {
        Authorization: `bearer ${gql?.token}`,
        'Content-Type': 'application/json',
      },
    }
  );
};

export const queryLibrarySearch = async ({
  gql,
  config,
  keyword,
  abortSignal,
  failOnNoLibraries = true,
}: {
  gql: GQLApi;
  config: Window['config'];
  keyword: string;
  abortSignal?: AbortSignal;
  failOnNoLibraries?: boolean;
}) => {
  const librariesResult = await gql?.getLibraries(abortSignal);
  const index = librariesResult?.libraries.records.map(
    (library) => `library:${library.id}`
  );
  if (!index || index.length === 0) {
    console.warn('Get libraries failed or none found');
    if (failOnNoLibraries) {
      throw new Error('No libraries found 2');
    } else {
      return;
    }
  }
  return await axios.post<
    AutocompleteRequest,
    AxiosResponse<AutocompleteResponse>
  >(
    `${config?.apiRoot ?? 'missing api root'}/api/search/autocomplete`,
    {
      limit: 10,
      index,
      fields: ['libraryName'],
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'term',
            field: 'identifierType',
            value: 'face',
          },
        ],
      },
      returnContext: true,
      text: keyword,
    },
    {
      headers: {
        Authorization: `bearer ${gql?.token}`,
        'Content-Type': 'application/json',
      },
    }
  );
};

const searchLibrariesAndEntitiesForString = async (
  keyword: string,
  gql: GQLApi,
  config: Window['config'],
  abortSignal?: AbortSignal,
  failOnNoLibraries = true
) => {
  if (!gql?.token) {
    throw new Error('GQLApi token is required for face recognition search');
  }

  const entitySearch = await queryEntitySearch({
    config,
    gql,
    keyword,
    abortSignal,
    failOnNoLibraries,
  });

  const librarySearch = await queryLibrarySearch({
    config,
    gql,
    keyword,
    abortSignal,
    failOnNoLibraries,
  });

  return [
    ...(entitySearch?.data.fields?.entityName?.map((entity) => [
      {
        operator: 'term',
        field: 'face-recognition.series.entityId',
        value: entity.doc.entityId,
      },
      {
        operator: 'term',
        field: 'face-recognition.series.libraryId',
        value: entity.doc.libraryId,
      },
    ]) ?? []),
    ...(librarySearch?.data.fields?.libraryName?.map((library) => [
      {
        operator: 'term',
        field: 'face-recognition.series.entityId',
        value: library.doc.entityId,
      },
      {
        operator: 'term',
        field: 'face-recognition.series.libraryId',
        value: library.doc.libraryId,
      },
    ]) ?? []),
  ].flat();
};
export const queryObjectSearch = async ({
  gql,
  config,
  params,
  abortSignal,
}: {
  gql: GQLApi;
  config: Window['config'];
  params: QueryObjectSearchParams;
  abortSignal?: AbortSignal;
}) => {
  if (!gql?.token) {
    throw new Error('GQLApi token is required for object detection search');
  }

  return await axios.post<
    AutocompleteRequest,
    AxiosResponse<AutocompleteResponse>
  >(
    `${config?.apiRoot ?? 'missing api root'}/api/search/autocomplete`,
    {
      limit: params.pagination?.ungrouped?.limit ?? 10,
      offset: params.pagination?.ungrouped?.offset ?? 0,
      index: ['mine', 'global'],
      fields: ['object-recognition.series.found'],
      returnContext: false,
      text: params.keywordSearchQuery ?? '',
    },
    {
      signal: abortSignal,
      headers: {
        Authorization: `bearer ${gql?.token}`,
        'Content-Type': 'application/json',
      },
    }
  );
};

const searchOptionMapping: Record<
  ResultCategory,
  {
    categoryId?: string;
    generateCondition: (
      params: FileSearchParams,
      generateForKeyword?: boolean,
      gql?: GQLApi,
      config?: Window['config'],
      abortSignal?: AbortSignal
    ) => AsyncOrSync<Query | undefined>;
    placeholder: string;
    description: string;
  }
> = {
  filename: {
    categoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
    generateCondition: (
      params: FileSearchParams,
      generateForKeyword = false
    ) => {
      if (generateForKeyword && params.keywordSearchQuery) {
        return {
          operator: 'query_string',
          field: 'veritone-file.filename',
          value: `*${params.keywordSearchQuery}*`,
        };
      } else if (params.cognitionFilters?.filename) {
        return {
          operator: 'query_string',
          field: 'veritone-file.filename',
          value: `*${params.cognitionFilters?.filename}*`,
        };
      }
    },
    placeholder: 'Words in Transcription',
    description: 'Search by keyword within our database of transcripts.',
  },
  transcription: {
    categoryId: '67cd4dd0-2f75-445d-a6f0-2f297d6cd182',
    generateCondition: (
      params: FileSearchParams,
      generateForKeyword = false
    ) => {
      if (generateForKeyword && params.keywordSearchQuery) {
        return {
          // See advanced search bar for more information on the query
          operator: 'query_string',
          field: 'transcript.transcript',
          value: params.keywordSearchQuery.toLowerCase() || '',
        };
      } else if (
        params.cognitionFilters &&
        !!params.cognitionFilters.wordsInTranscript
      ) {
        return {
          // See advanced search bar for more information on the query
          operator: 'query_string',
          field: 'transcript.transcript',
          value: params.cognitionFilters.wordsInTranscript.toLowerCase(),
        };
      }
    },
    placeholder: 'Words in Transcription',
    description: 'Search by keyword within our database of transcripts.',
  },
  faceRecognition: {
    // TODO: This will require a UI similar to advanced search bar to select entity or library
    categoryId: '6faad6b7-0837-45f9-b161-2f6bf31b7a07',
    generateCondition: async (
      params: FileSearchParams,
      generateForKeyword = false,
      gql?: GQLApi,
      config?: Window['config'],
      abortSignal?: AbortSignal
    ) => {
      if (!gql && generateForKeyword && params.keywordSearchQuery) {
        throw new Error(
          'GQLApi instance is required for face recognition search'
        );
      }

      if (generateForKeyword && params.keywordSearchQuery) {
        if (!gql || !config) {
          throw new Error(
            'GQLApi and config instance are required for face recognition search'
          );
        }

        const conditions = await searchLibrariesAndEntitiesForString(
          params.keywordSearchQuery,
          gql,
          config,
          abortSignal,
          false
        );

        if (conditions && conditions.length > 0) {
          return {
            operator: 'or',
            conditions,
          };
        }
      } else {
        if (
          !params.cognitionFilters?.faceDetection?.terms ||
          params.cognitionFilters?.faceDetection?.terms.length === 0
        ) {
          return;
        }

        const newTerms: FaceDetectionTerm[] = [];

        await forEachSeries(
          params.cognitionFilters.faceDetection.terms,
          // TODO: p-iteration types do not correctly declare that the callback can return a Promise<void>
          // consider finding a different library or just moving the necessary code into the app
          // eslint-disable-next-line @typescript-eslint/no-misused-promises
          async (term) => {
            if (term.type === 'string' && term.stringQuery && gql && config) {
              const conditions = await searchLibrariesAndEntitiesForString(
                term.stringQuery,
                gql,
                config
              );

              if (conditions.length === 1) {
                if (conditions[0].field.includes('library')) {
                  newTerms.push({
                    type: 'library',
                    libraryId: conditions[0].value,
                  });
                }
                if (conditions[0].field.includes('entity')) {
                  newTerms.push({
                    type: 'entity',
                    entityId: conditions[0].value,
                  });
                }
              } else if (conditions.length > 1) {
                newTerms.push({
                  type: 'operator',
                  operator: MathOperators.OPEN_PARENTHESIS,
                });

                for (let i = 0; i < conditions.length; i++) {
                  if (conditions[i].field.includes('library')) {
                    newTerms.push({
                      type: 'library',
                      libraryId: conditions[i].value,
                    });
                  } else if (conditions[i].field.includes('entity')) {
                    newTerms.push({
                      type: 'entity',
                      entityId: conditions[i].value,
                    });
                  }
                  if (i !== conditions.length - 1) {
                    newTerms.push({
                      type: 'operator',
                      operator: MathOperators.OR,
                    });
                  }
                }
                newTerms.push({
                  type: 'operator',
                  operator: MathOperators.CLOSE_PARENTHESIS,
                });
              }
            } else {
              newTerms.push(term);
            }
          }
        );

        return buildTreeFromLogic(
          newTerms.map((term) => ({
            type: term.type,
            operator: term.operator,
            entityId: term.entityId,
            libraryId: term.libraryId,
            children: [],
          }))
        )?.query;
      }
    },
    placeholder: 'Face Detection',
    description: 'Search by known images of people within our database.',
  },
  objectDetection: {
    categoryId: '088a31be-9bd6-4628-a6f0-e4004e362ea0',
    generateCondition: async (
      params: FileSearchParams,
      generateForKeyword = false,
      gql?: GQLApi,
      config?: Window['config'],
      abortSignal?: AbortSignal
    ) => {
      if (!gql || !config) {
        throw new Error('GQLApi and config are required for this operation');
      }
      if (generateForKeyword && params.keywordSearchQuery) {
        const objectSearch = await queryObjectSearch({
          gql,
          config,
          params,
          abortSignal,
        });

        if (
          objectSearch.data.fields?.['object-recognition.series.found'] &&
          objectSearch.data.fields?.['object-recognition.series.found']
            ?.length > 0
        ) {
          return {
            operator: 'or',
            conditions: objectSearch.data.fields?.[
              'object-recognition.series.found'
            ]?.map((object) => ({
              operator: 'query_string',
              field: 'object-recognition.series.found.fulltext',
              value: `*${object.key}*`,
            })),
          };
        }
      } else {
        if (
          !params.cognitionFilters?.objectDetection?.terms ||
          params.cognitionFilters?.objectDetection?.terms.length === 0
        ) {
          return;
        }

        return buildTreeFromLogic(
          params.cognitionFilters.objectDetection.terms.map((term) => ({
            type: term.type,
            operator: term.operator,
            foundFullTextString: term.foundFullTextString,
            foundString: term.foundString,
            children: [],
          }))
        )?.query;
      }
    },
    placeholder: 'Object Descriptors',
    description: 'Search by objects within our database.',
  },
  vehicleRecognition: {
    generateCondition: (
      params: FileSearchParams,
      generateForKeyword = false
    ) => {
      if (generateForKeyword && params.keywordSearchQuery) {
        return {
          operator: 'or',
          conditions: params.keywordSearchQuery
            .split(' ')
            .map((term) => [
              {
                operator: 'query_string',
                field: 'motorVehicle.series.motorVehicle.make.fulltext',
                value: `*${escapeBadChars(term.toLowerCase())}*`,
              },
              {
                operator: 'query_string',
                field: 'motorVehicle.series.motorVehicle.model.fulltext',
                value: `*${escapeBadChars(term.toLowerCase())}*`,
              },
            ])
            .flat(),
        };
      } else if (params.cognitionFilters?.vehicleDetection) {
        return {
          operator: 'or',
          conditions: params.cognitionFilters?.vehicleDetection
            .split(' ')
            .map((term) => [
              {
                operator: 'query_string',
                field: 'motorVehicle.series.motorVehicle.make.fulltext',
                value: `*${escapeBadChars(term.toLowerCase())}*`,
              },
              {
                operator: 'query_string',
                field: 'motorVehicle.series.motorVehicle.model.fulltext',
                value: `*${escapeBadChars(term.toLowerCase())}*`,
              },
            ])
            .flat(),
        };
      }
    },
    categoryId: '',
    placeholder: '',
    description: '',
  },
  licensePlateRecognition: {
    generateCondition: (
      params: FileSearchParams,
      generateForKeyword = false
    ) => {
      if (generateForKeyword && params.keywordSearchQuery) {
        return {
          operator: 'query_string',
          field: 'licensePlate.series.licensePlate.number.fulltext',
          value: `*${escapeBadChars(params.keywordSearchQuery.toLowerCase())}*`,
        };
      } else if (params.cognitionFilters?.licensePlateDetection) {
        return {
          operator: 'query_string',
          field: 'licensePlate.series.licensePlate.number.fulltext',
          value: `*${escapeBadChars(params.cognitionFilters.licensePlateDetection.toLowerCase())}*`,
        };
      }
    },
    categoryId: '',
    placeholder: '',
    description: '',
  },
  // sceneClassification: {
  //   categoryId: '',
  //   generateCondition: async (
  //     params: FileSearchParams,
  //     generateForKeyword = false
  //   ) => {
  //     if (generateForKeyword && params.keywordSearchQuery) {
  //       return {
  //         operator: 'query_object',
  //         field: 'object-recognition.series',
  //         query: {
  //           operator: 'and',
  //           conditions: [
  //             {
  //               operator: 'query_string',
  //               field: 'found.fulltext',
  //               value: params.keywordSearchQuery?.toLowerCase(),
  //             },
  //           ],
  //         },
  //       };
  //     } else if (params.cognitionFilters?.sceneDetection) {
  //       return {
  //         operator: 'query_object',
  //         field: 'object-recognition.series',
  //         query: {
  //           operator: 'and',
  //           conditions: [
  //             {
  //               operator: 'query_string',
  //               field: 'found.fulltext',
  //               value: params.cognitionFilters?.sceneDetection?.toLowerCase(),
  //             },
  //           ],
  //         },
  //       };
  //     }
  //   },
  //   placeholder: 'Scene Classification',
  //   description: 'Search by scene classification within our database.',
  // },
  textRecognition: {
    categoryId: '3b4ac603-9bfa-49d3-96b3-25ca3b502325',
    generateCondition: (
      params: FileSearchParams,
      generateForKeyword = false
    ) => {
      if (generateForKeyword && params.keywordSearchQuery) {
        return {
          operator: 'query_string',
          field: 'text-recognition.series.ocrtext',
          value: `*${params.keywordSearchQuery?.toLowerCase()}*`,
        };
      } else if (params.cognitionFilters?.textRecognition) {
        const searchString =
          params.cognitionFilters?.textRecognition?.toLowerCase();

        return {
          operator: 'query_string',
          field: 'text-recognition.series.ocrtext',
          value: searchString === '*' ? '*' : `*${searchString}*`,
        };
      }
    },
    placeholder: 'Text Recognition',
    description: 'Searches within our database for recognized text.',
  },
  metadata: {
    // @ts-expect-error TODO: Fix the type here
    generateCondition: async (
      params: FileSearchParams,
      generateForKeyword = false,
      gql,
      config: Window['config'],
      abortSignal?: AbortSignal
    ) => {
      const schemaProperties = await gql?.getSchemaProperties(
        config?.dataRegistry.evidenceTypeRegistry?.id ??
          '6dc74ccc-5d01-4fa7-a41f-9c6d29e4692d',
        abortSignal
      );

      if (
        generateForKeyword
          ? !!params.keywordSearchQuery
          : !!params?.cognitionFilters?.metadata
      ) {
        return {
          operator: 'or',
          conditions: [
            'evidenceType', // TODO: maybe(?) pull these from the schema props, so we don't have to update this list if a schema field is added or removed
            'cadId',
            'badgeId',
            'deviceId',
            'lastName',
            'firstName',
            'cameraType',
            'deviceName',
            'deviceType',
            'unitNumber',
            'citizenName',
            'dateOfBirth',
            'deviceModel',
            'interviewee',
            'interviewer',
            'officerName',
            'reportNumber',
            'interviewRoom',
            'locationTimeline.latitude',
            'locationTimeline.longitude',
            'callerPhoneNumber',
            'evidenceTechnician',
            'cameraFacingDirection',
            'deviceRegisteredOwner',
            'cameraPhysicalLocation',
          ]
            .map((field) => {
              const searchpath =
                schemaProperties?.schemaProperties.records.find(
                  (record) => record.path === field
                )?.searchPath;

              return searchpath
                ? {
                    operator: 'query_string',
                    field: `${searchpath}.fulltext`,
                    value: `*${generateForKeyword ? params.keywordSearchQuery : params?.cognitionFilters?.metadata}*`,
                  }
                : undefined;
            })
            .filter((q) => q !== undefined),
        };
      }
    },
    placeholder: 'Search for Text - Metadata',
    description: 'Search file metadata fields',
  },
};

const generateUploadDateCondition = (params: FileSearchParams) => {
  // Filter by date range

  if (
    params.uploadDateFilter &&
    params.uploadDateFilter?.startDate &&
    params.uploadDateFilter?.endDate
  ) {
    const startDate = new Date(params.uploadDateFilter.startDate);
    const endDate = new Date(params.uploadDateFilter.endDate);
    startDate.setHours(0, 0, 0, 0); // beginning of the day
    endDate.setHours(23, 59, 59, 999); // end of the day

    return {
      operator: 'range',
      field: 'createdTime',
      gte: startDate.toISOString(),
      lte: endDate.toISOString(),
    };
  }
};

const generateCaseIdFilterCondition = async (
  params: FileSearchParams,
  gql: GQLApi,
  config: Window['config'],
  abortSignal?: AbortSignal
) => {
  const caseRegistryName = config.dataRegistry.caseRegistry.name;
  const caseSchemaId = await gql.getSDOSchemaIdByName(
    caseRegistryName,
    abortSignal
  );

  if (
    params.caseIdFilter &&
    params.caseIdFilter?.filter((caseId) => caseId !== '')?.length > 0
  ) {
    const casesResults = await gql.searchCaseByIds({
      caseIds: params.caseIdFilter,
      caseSchemaId,
      abortSignal,
    });

    const folders = [
      ...new Set(
        casesResults.searchMedia.jsondata.results.map(
          (result) => result.folderId
        )
      ),
    ];

    // This will force no results if no results are found for given caseId
    if (folders.length === 0) {
      return {
        operator: 'terms',
        field: 'parentTreeObjectIds',
        values: [],
      };
    }

    const folderTreeObjectIds = await map(folders, async (folderId) => {
      const folder = await gql.getFolder({ folderId });
      return folder.treeObjectId;
    });

    if (folderTreeObjectIds.length === 0) {
      return;
    }

    return {
      operator: 'terms',
      field: 'parentTreeObjectIds',
      values: folderTreeObjectIds,
    };
  }
};

const generateCaseStatusCondition = async (
  params: FileSearchParams,
  gql: GQLApi,
  config: Window['config'],
  abortSignal?: AbortSignal
) => {
  const caseRegistryName = config.dataRegistry.caseRegistry.name;
  const caseSchemaId = await gql.getSDOSchemaIdByName(caseRegistryName);
  const { caseStatusesFilter: condition } = params;

  if (
    condition &&
    condition.values.filter((caseId) => caseId !== '').length > 0
  ) {
    const casesResults = await gql.searchCaseByCondition({
      caseStatusesContidion: condition,
      caseSchemaId,
      abortSignal,
    });

    const folders = [
      ...new Set(
        casesResults.searchMedia.jsondata.results.map(
          (result) => result.folderId
        )
      ),
    ];

    if (!folders.length) {
      return;
    }

    const { treeObjectIds } = await gql.getMultiFolders(folders);

    if (!treeObjectIds.length) {
      return;
    }

    return {
      field: 'parentTreeObjectIds',
      operator: 'terms',
      values: treeObjectIds,
    };
  }
};

const generateCaseTagCondition = async (
  params: FileSearchParams,
  gql: GQLApi,
  config: Window['config'],
  abortSignal?: AbortSignal
) => {
  const caseRegistryName = config.dataRegistry.caseRegistry.name;
  const caseSchemaId = await gql.getSDOSchemaIdByName(caseRegistryName);
  const { caseTagsFilter: condition } = params;

  if (
    condition &&
    condition.values.filter((caseId) => caseId !== '').length > 0
  ) {
    const casesResults = await gql.searchCaseByCondition({
      caseTagsCondition: condition,
      caseSchemaId,
      abortSignal,
    });

    const folders = [
      ...new Set(
        casesResults.searchMedia.jsondata.results.map(
          (result) => result.folderId
        )
      ),
    ];
    const { treeObjectIds } = await gql.getMultiFolders(folders);

    if (treeObjectIds.length === 0) {
      return;
    }

    return {
      operator: 'terms',
      field: 'parentTreeObjectIds',
      values: treeObjectIds,
    };
  }
};

const generateFileEvidenceTypeFilter = async (
  params: FileSearchParams,
  gql: GQLApi,
  config: Window['config'],
  aboortSignal?: AbortSignal
) => {
  const fileContentTemplateDataRegistryId =
    config.dataRegistry.evidenceTypeRegistry?.id ??
    '6dc74ccc-5d01-4fa7-a41f-9c6d29e4692d';

  if (params.evidenceTypeFilter && params.evidenceTypeFilter.length > 0) {
    const schemaProps = await gql.getSchemaProperties(
      fileContentTemplateDataRegistryId,
      aboortSignal
    );

    if (schemaProps.schemaProperties.records.length === 0) {
      throw new Error('No schema properties found');
    }

    const evidenceTypeSearchPath = schemaProps.schemaProperties.records.find(
      (record) => record.path === 'evidenceType'
    )?.searchPath;

    if (!evidenceTypeSearchPath) {
      throw new Error('No evidenceType search path found');
    }

    return {
      operator: 'or',
      conditions: params.evidenceTypeFilter.map((evidenceType) => ({
        operator: 'term',
        field: evidenceTypeSearchPath,
        value: evidenceType,
      })),
    };
  }
};

const generateFileTypeCondition = (params: FileSearchParams) => {
  const query: {
    operator: string;
    conditions: { operator: string; field: string; value: string }[];
  } = {
    operator: 'or',
    conditions: [],
  };

  if (params.fileTypeFilter && params.fileTypeFilter.length > 0) {
    if (params.fileTypeFilter.includes('Image')) {
      query.conditions.push({
        operator: 'query_string',
        field: 'fileType',
        value: 'image*',
      });
    }
    if (params.fileTypeFilter.includes('Audio')) {
      query.conditions.push({
        operator: 'query_string',
        field: 'fileType',
        value: 'audio*',
      });
    }
    if (params.fileTypeFilter.includes('Video')) {
      query.conditions.push({
        operator: 'query_string',
        field: 'fileType',
        value: 'video*',
      });
    }
    if (params.fileTypeFilter.includes('Document')) {
      query.conditions.push({
        operator: 'query_string',
        field: 'fileType',
        value: 'application*',
      });
      query.conditions.push({
        operator: 'query_string',
        field: 'fileType',
        value: 'text*',
      });
    }
  } else {
    return;
  }
  return query;
};

const generateFolderIdFilterCondition = async (
  params: FileSearchParams,
  gql: GQLApi
) => {
  if (
    params.folderIdsFilter &&
    params.folderIdsFilter?.filter((caseId) => caseId !== '')?.length > 0
  ) {
    const folderTreeObjectIds = await map(
      params.folderIdsFilter,
      async (folderId) => {
        const folder = await gql.getFolder({ folderId });
        return folder.treeObjectId;
      }
    );

    if (folderTreeObjectIds.length === 0) {
      return;
    }

    return {
      operator: 'terms',
      field: 'parentTreeObjectIds',
      values: folderTreeObjectIds,
    };
  }
};

const generateSort = (params: FileSearchParams) => [
  {
    field: params.sort?.type ?? 'veritone-file.filename',
    order: params?.sort?.order ?? 'asc',
  },
];

const noResult = () => ({
  searchMedia: {
    jsondata: {
      results: [],
      totalResults: {
        value: 0,
        relation: '',
      },
      limit: 10,
      from: 0,
      to: 0,
      searchToken: '',
      timestamp: Date.now(),
    },
  },
});

// TODO: Is this always guaranteed to be reversible?
const mkNewVars = () =>
  cloneDeep(fileSearchVars) as typeof fileSearchVars & {
    search: { sort?: ReturnType<typeof generateSort> };
  };

const pushIfDefined = (
  values: (ArrayOrSingle<Query> | undefined)[],
  array?: Query[]
) => {
  if (values && values.length > 0) {
    array?.push(
      ...values.flat().filter((value) => value !== undefined && value !== null)
    );
  }
};

const fillEmptyOrUndefinedCognition = (
  params: FileSearchParams,
  category: string
) => {
  const filledParams = cloneDeep(params);
  filledParams.cognitionFilters = { ...filledParams.cognitionFilters };

  switch (category) {
    case 'transcription':
      filledParams.cognitionFilters.wordsInTranscript = !filledParams
        .cognitionFilters.wordsInTranscript
        ? '*'
        : filledParams.cognitionFilters.wordsInTranscript;
      break;

    case 'faceRecognition':
      if (!filledParams.cognitionFilters?.faceDetection?.terms) {
        filledParams.cognitionFilters.faceDetection = {
          terms: [],
        };
      }

      filledParams.cognitionFilters.faceDetection.terms =
        filledParams.cognitionFilters.faceDetection.terms.length === 0
          ? [
              { type: 'library', libraryId: '*' },
              { type: 'operator', operator: MathOperators.OR },
              { type: 'entity', entityId: '*' },
            ]
          : filledParams.cognitionFilters.faceDetection.terms;
      break;

    case 'objectDetection':
      if (!filledParams.cognitionFilters?.objectDetection?.terms) {
        filledParams.cognitionFilters.objectDetection = {
          terms: [],
        };
      }

      filledParams.cognitionFilters.objectDetection.terms =
        filledParams.cognitionFilters.objectDetection.terms.length === 0
          ? [{ type: 'foundString', foundString: '*' }]
          : filledParams.cognitionFilters.objectDetection.terms;
      break;

    case 'vehicleRecognition':
      filledParams.cognitionFilters.vehicleDetection = !filledParams
        .cognitionFilters.vehicleDetection
        ? '*'
        : filledParams.cognitionFilters.vehicleDetection;
      break;

    case 'licensePlateRecognition':
      filledParams.cognitionFilters.licensePlateDetection = !filledParams
        .cognitionFilters.licensePlateDetection
        ? '*'
        : filledParams.cognitionFilters.licensePlateDetection;
      break;

    // case 'sceneClassification':
    //   filledParams.cognitionFilters.sceneDetection = !filledParams
    //     .cognitionFilters.sceneDetection
    //     ? '*'
    //     : filledParams.cognitionFilters.sceneDetection;
    //   break;

    case 'textRecognition':
      filledParams.cognitionFilters.textRecognition = !filledParams
        .cognitionFilters.textRecognition
        ? '*'
        : filledParams.cognitionFilters.textRecognition;
      break;

    case 'metadata':
      filledParams.cognitionFilters.metadata = !filledParams.cognitionFilters
        .metadata
        ? '*'
        : filledParams.cognitionFilters.metadata;
      break;
  }

  return filledParams;
};

const generateSharedConditions = async (
  params: FileSearchParams,
  gql: GQLApi,
  config: Window['config']
): Promise<SearchConditions> => {
  const caseIdConditions = await generateCaseIdFilterCondition(
    params,
    gql,
    config
  );
  const caseStatusConditions = await generateCaseStatusCondition(
    params,
    gql,
    config
  );
  const caseTagConditions = await generateCaseTagCondition(params, gql, config);
  const evidenceTypeFilterCondition = await generateFileEvidenceTypeFilter(
    params,
    gql,
    config
  );

  const fileTypeFilterCondition = generateFileTypeCondition(params);
  const uploadDateFilterCondition = generateUploadDateCondition(params);
  const sortCondition = generateSort(params);
  const folderIdFilterCondition = await generateFolderIdFilterCondition(
    params,
    gql
  );

  const noneSoftDeletedCondition = {
    operator: 'query_object',
    field: 'tags',
    not: true,
    query: {
      operator: 'query_string',
      field: 'tags.key',
      value: 'toBeDeletedTime',
      dotNotation: true,
    },
  };

  return {
    caseIdConditions,
    caseStatusConditions,
    caseTagConditions,
    evidenceTypeFilterCondition,
    fileTypeFilterCondition,
    uploadDateFilterCondition,
    sortCondition,
    folderIdFilterCondition,
    noneSoftDeletedCondition,
  };
};

async function searchFilesGrouped({
  params,
  gql,
  dispatch,
  config,
  lastParams,
  abortSignal,
  conditions,
}: SearchFilesSharedParams & { conditions: SearchConditions }) {
  const { checkedResultCategories, pagination } = params;

  const isAnyPaginationAnUpdate =
    Object.values(pagination).some((page) => page?.isUpdate) && !!lastParams;

  const {
    uploadDateFilterCondition,
    caseIdConditions,
    caseStatusConditions,
    caseTagConditions,
    evidenceTypeFilterCondition,
    fileTypeFilterCondition,
    folderIdFilterCondition,
    sortCondition,
    noneSoftDeletedCondition,
  } = conditions;

  // TODO: p-iteration types do not correctly declare that the callback can return a Promise<void>
  // consider finding a different library or just moving the necessary code into the app
  // eslint-disable-next-line @typescript-eslint/no-misused-promises
  await forEach(checkedResultCategories, async (category) => {
    if (category in searchOptionMapping) {
      const newFileSearch = mkNewVars();

      try {
        const newPagination = {
          ...pagination,
          [category]: {
            ...pagination[category],
            isUpdate: false,
          },
        };

        if (isAnyPaginationAnUpdate && !pagination[category]?.isUpdate) {
          // Data is not updated in this case
          dispatch(
            setFileSearchResult({
              status: 'success',
              params,
              pagination: newPagination,
              isGrouped: true,
              category,
              queryVars: newFileSearch,
            })
          );
          return;
        }

        newFileSearch.search.sort = sortCondition;
        newFileSearch.search.limit = pagination[category]?.limit ?? 10;
        newFileSearch.search.offset = pagination[category]?.offset ?? 0;

        if (newFileSearch.search.query.conditions) {
          const keywordConditions = await searchOptionMapping[
            category
          ].generateCondition(params, true, gql, config, abortSignal);

          const cognitionsConditions = await searchOptionMapping[
            category
          ].generateCondition(
            fillEmptyOrUndefinedCognition(params, category),
            false,
            gql,
            config,
            abortSignal
          );

          pushIfDefined(
            [
              keywordConditions,
              cognitionsConditions,
              uploadDateFilterCondition,
              caseIdConditions,
              caseStatusConditions,
              caseTagConditions,
              evidenceTypeFilterCondition,
              fileTypeFilterCondition,
              folderIdFilterCondition,
            ],
            newFileSearch.search.query.conditions
          );
        } else {
          dispatch(
            setFileSearchResult({
              status: 'failure',
              params,
              pagination: newPagination,
              isGrouped: true,
              category,
              queryVars: newFileSearch,
            })
          );
          throw new Error('No conditions found');
        }

        const noConditions =
          newFileSearch.search.query.conditions &&
          newFileSearch.search.query.conditions.length === 0;
        if (noConditions) {
          dispatch(
            setFileSearchResult({
              status: 'success',
              params,
              pagination: newPagination,
              isGrouped: true,
              category,
              data: noResult(),
              queryVars: newFileSearch,
            })
          );
        } else {
          // Search for individual category
          newFileSearch.search.query.conditions.push(noneSoftDeletedCondition);
          const searchResults = await gql.searchFiles({
            searchVars: optimizeQuery(newFileSearch),
            queryName: `${category}FileSearch`,
            abortSignal,
          });
          dispatch(
            setFileSearchResult({
              status: 'success',
              params,
              pagination: newPagination,
              isGrouped: true,
              category,
              data: searchResults,
              queryVars: newFileSearch,
            })
          );
        }
      } catch (_e) {
        dispatch(
          setFileSearchResult({
            status: abortSignal?.aborted ? 'aborted' : 'failure',
            params,
            pagination,
            isGrouped: true,
            category,
            queryVars: newFileSearch,
          })
        );
      }
    } else {
      dispatch(
        setFileSearchResult({
          status: abortSignal?.aborted ? 'aborted' : 'failure',
          params,
          pagination,
          isGrouped: true,
          category,
        })
      );
      throw new Error('Category not found');
    }
  });
}

async function searchFilesUngrouped({
  params,
  gql,
  dispatch,
  config,
  abortSignal,
  isCaseDetails,
  conditions,
}: SearchFilesSharedParams & { conditions: SearchConditions }) {
  const { checkedResultCategories, pagination, keywordSearchQuery } = params;

  const {
    uploadDateFilterCondition,
    caseIdConditions,
    caseStatusConditions,
    caseTagConditions,
    evidenceTypeFilterCondition,
    fileTypeFilterCondition,
    folderIdFilterCondition,
    sortCondition,
    noneSoftDeletedCondition,
  } = conditions;

  const newFileSearch = mkNewVars();

  newFileSearch.search.sort = sortCondition;
  newFileSearch.search.limit = pagination.ungrouped?.limit ?? 10;
  newFileSearch.search.offset = pagination.ungrouped?.offset ?? 0;

  if (newFileSearch.search.query.conditions) {
    if (keywordSearchQuery) {
      const conditions = await map(
        checkedResultCategories,
        async (category) => {
          if (category in searchOptionMapping) {
            return await searchOptionMapping[category].generateCondition(
              params,
              true,
              gql,
              config
            );
          } else {
            throw new Error('Category not found');
          }
        }
      );
      const filteredConditions = conditions.filter(
        (condition) => condition !== undefined && condition !== null
      );

      if (conditions && conditions.length > 0) {
        newFileSearch.search.query.conditions.push({
          operator: 'or',
          conditions: filteredConditions,
        });
      }
    }

    pushIfDefined(
      [
        uploadDateFilterCondition,
        caseIdConditions,
        caseStatusConditions,
        caseTagConditions,
        evidenceTypeFilterCondition,
        fileTypeFilterCondition,
        folderIdFilterCondition,
      ],
      newFileSearch.search.query.conditions
    );

    // TODO: p-iteration types do not correctly declare that the callback can return a Promise<void>
    // consider finding a different library or just moving the necessary code into the app
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    await forEach(checkedResultCategories, async (category) => {
      if (category in searchOptionMapping) {
        const condition = await searchOptionMapping[category].generateCondition(
          params,
          false,
          gql,
          config
        );

        if (newFileSearch.search.query.conditions && condition) {
          newFileSearch.search.query.conditions.push(condition);
        }
      } else {
        throw new Error('Category not found');
      }
    });
  } else {
    throw new Error('No conditions found');
  }

  newFileSearch.search.query.conditions.push(noneSoftDeletedCondition);
  // Search for all categories in the same search
  const searchResults = await gql.searchFiles({
    searchVars: optimizeQuery(newFileSearch),
    queryName: `ungroupedFileSearch`,
    abortSignal,
  });
  dispatch(
    setFileSearchResult({
      status: 'success',
      params,
      pagination,
      isGrouped: false,
      data: searchResults,
      queryVars: newFileSearch,
      isCaseDetails,
    })
  );
}

export async function searchFiles({
  params,
  gql,
  dispatch,
  config,
  lastParams,
  abortSignal,
  isPolling,
  isCaseDetails,
  token,
}: SearchFilesSharedParams) {
  params.keywordSearchQuery =
    params.keywordSearchQuery !== undefined
      ? escapeBadChars(params.keywordSearchQuery)
      : undefined;

  const { searchResultType, checkedResultCategories } = params;

  if (lastParams && isEqual(params, lastParams && !isPolling)) {
    return;
  }

  // Generate shared conditions once
  const conditions = await generateSharedConditions(params, gql, config);

  const curStatus = isPolling ? 'success' : 'loading';

  if (searchResultType === 'ungrouped') {
    dispatch(
      setFileSearchResult({ status: curStatus, isGrouped: false, params })
    );
    await searchFilesUngrouped({
      params,
      gql,
      dispatch,
      config,
      lastParams,
      abortSignal,
      isPolling,
      isCaseDetails,
      token,
      conditions,
    });
  } else if (searchResultType === 'grouped') {
    checkedResultCategories.forEach((category) => {
      dispatch(
        setFileSearchResult({
          status: 'loading',
          category,
          isGrouped: true,
          params,
        })
      );
    });
    searchFilesGrouped({
      params,
      gql,
      dispatch,
      config,
      lastParams,
      abortSignal,
      isPolling,
      isCaseDetails,
      token,
      conditions,
    });
  } else {
    dispatch(
      setFileSearchResult({ status: curStatus, isGrouped: false, params })
    );
    checkedResultCategories.forEach((category) => {
      dispatch(
        setFileSearchResult({
          status: 'loading',
          category,
          isGrouped: true,
          params,
        })
      );
    });
    await Promise.all([
      searchFilesUngrouped({
        params,
        gql,
        dispatch,
        config,
        lastParams,
        abortSignal,
        isPolling,
        isCaseDetails,
        token,
        conditions,
      }),
      searchFilesGrouped({
        params,
        gql,
        dispatch,
        config,
        lastParams,
        abortSignal,
        isPolling,
        isCaseDetails,
        token,
        conditions,
      }),
    ]);
  }
}
