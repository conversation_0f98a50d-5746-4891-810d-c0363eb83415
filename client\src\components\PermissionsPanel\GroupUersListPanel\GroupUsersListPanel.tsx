import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { I18nTranslate } from '@i18n';
import {
  Avatar,
  Box,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { Close as CloseIcon, Search as SearchIcon } from '@mui/icons-material';
import { userOrGroupOption } from '@components/PermissionsPanel';
import './GroupUsersListPanel.scss';
import { useState } from 'react';
import { User } from '@utils/olp/types.ts';

interface GroupUsersListPanelProps {
  onClose: () => void;
  currentGroup: userOrGroupOption;
}

const GroupUsersListPanel = ({
  onClose,
  currentGroup,
}: GroupUsersListPanelProps) => {
  const intl = I18nTranslate.Intl();
  const [groupUsers, setGroupUsers] = useState<userOrGroupOption>(currentGroup);
  const [inputValue, setInputValue] = useState('');

  const filterUsers = (searchText: string) => {
    if (!currentGroup.members) {
      return;
    }

    const filteredMembers = currentGroup.members.filter((user) =>
      `${user.firstName} ${user.lastName}`
        .toLowerCase()
        .includes(searchText.toLowerCase())
    );

    setGroupUsers({
      ...currentGroup,
      members: filteredMembers,
    });
  };

  const renderGroupUsers = () => {
    const getFirstLastInitials = (user: User) => {
      const firstNameInitial = user.firstName ? user.firstName[0] : '';
      const lastNameInitial = user.lastName ? user.lastName[0] : '';
      return `${firstNameInitial}${lastNameInitial}`;
    };

    if (!groupUsers || !groupUsers.members || groupUsers.members.length === 0) {
      return;
    }

    return groupUsers.members.map((user) => (
      <div key={user.id} className="group-users-list__user-container">
        <div className="group-users-list__user-avatar">
          {user.imageUrl ? (
            <img src={user.imageUrl} alt={user.firstName} />
          ) : (
            <Avatar sx={{ bgcolor: 'var(--button-text-disabled)' }}>
              {getFirstLastInitials(user)}
            </Avatar>
          )}
        </div>
        <div key={user.id} className="group-users-list__user-name">
          <div>
            {user.firstName} {user.lastName}
          </div>
          <div className={'group-users-list__email'}>{user.email}</div>
        </div>
      </div>
    ));
  };

  return (
    <div className="group-users-list-panel">
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <div className="groups-users-list__title-container">
          <div
            className="groups-users-list__heading-title"
            data-testid="groups-users-list-title"
          >
            {currentGroup.name}
          </div>
          <div>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </div>
        </div>
        <Box role="presentation" className="group-users-list__main-container">
          <div className={'group-users-list__subtitle-text'}>
            {I18nTranslate.TranslateMessage(
              'theseUsersHaveAccessToThisGroupsPermissions'
            )}
          </div>
          <div className="group-users-list__vertical-field-gap">
            <TextField
              fullWidth
              data-testid={'permissions-search-select'}
              placeholder={intl.formatMessage({
                id: 'searchForPeopleInThisGroup',
              })}
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value);
                filterUsers(e.target.value);
              }}
              slotProps={{
                htmlInput: {
                  'data-testid': 'permission-select-search-input',
                },
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize={'medium'} />
                    </InputAdornment>
                  ),
                  // className: cn('multi-select__search-input', {
                  //   'multi-select__filter-search-input': isFilterCaseTags,
                  // }),
                },
              }}
            />
          </div>
          <Box className="group-users-list__users-container">
            {renderGroupUsers()}
          </Box>
        </Box>
      </LocalizationProvider>
    </div>
  );
};

export default GroupUsersListPanel;
