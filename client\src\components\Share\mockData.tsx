import { SharedFile } from './ShareDetailsTable';
import { Share } from './ShareTable';

const generateMockShares = (count: number): Share[] => {
  const mockShares: Share[] = [];
  for (let i = 1; i <= count; i++) {
    mockShares.push({
      id: `${i}`,
      shareName: `Case #${45662990 + i} Evidence Package`,
      sharedBy: i % 2 === 0 ? '<PERSON><PERSON>' : '<PERSON><PERSON>',
      fileCount: Math.floor(Math.random() * 20) + 1,
      recipientCount: Math.floor(Math.random() * 10) + 1,
      expirationDate: new Date(2025, 3, 20, 10, 0, 0).toISOString(),
      status: i % 3 === 0 ? 'EXPIRED' : 'ACTIVE',
    });
  }
  return mockShares;
};
export const mockShares = generateMockShares(25);

// Data mock for shared details table

const sampleFiles = [
  { name: 'evidence-report-final.pdf', type: 'application/pdf' },
  { name: 'interview-footage-01.mp4', type: 'video/mp4' },
  { name: 'crime-scene-photo-A.jpg', type: 'image/jpeg' },
  { name: 'witness-statement.pdf', type: 'application/pdf' },
  { name: 'security-cam-B.mp4', type: 'video/mp4' },
  { name: 'audio-recording-witness.mp3', type: 'audio/mpeg' },
];
const generateMockSharedFiles = (count: number): SharedFile[] => {
  const mockFiles: SharedFile[] = [];
  const today = new Date();

  for (let i = 1; i <= count; i++) {
    const randomSample =
      sampleFiles[Math.floor(Math.random() * sampleFiles.length)];

    const randomDaysAgo = Math.floor(Math.random() * 365);
    const uploadDate = new Date(today);
    uploadDate.setDate(today.getDate() - randomDaysAgo);

    mockFiles.push({
      id: `file-${i}`,
      folderId: `folder-${Math.floor(Math.random() * 5) + 1}`,
      caseFile: `${i}_${randomSample.name}`,
      uploadDate: uploadDate.toISOString(),
      fileSize: `${Math.floor(Math.random() * 400) + 10} MB`,
      fileType: randomSample.type,
    });
  }
  return mockFiles;
};

export const mockSharedFiles = generateMockSharedFiles(15);
