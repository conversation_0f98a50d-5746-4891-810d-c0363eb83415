import { configureAppStore } from '@store/index';
import { ConfigSliceState } from '@store/modules/config/slice';
import * as metadataSlice from '@store/modules/metadata/slice';
import {
  MetadataSliceState,
  initialState as metadataInitialState,
} from '@store/modules/metadata/slice';
import {
  SearchSliceState,
  initialState as searchInitialState,
} from '@store/modules/search/slice';
import {
  CaseManagerSliceState,
  initialState as caseManagerInitialState,
} from '@store/modules/caseManager/slice';
import { waitFor } from '@testing-library/dom';
import { GQLApi } from '@utils/helpers';
import { Provider } from 'react-redux';
import {
  MemoryRouter,
  useLocation,
  useNavigate,
  useParams,
} from 'react-router';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CaseDetailsTable from '.';
import { render } from '../../../../test/render';

const mockEnqueueSnackbar = vi.fn();
const mockCloseSnackbar = vi.fn();

vi.mock(import('notistack'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useSnackbar: () => ({
      enqueueSnackbar: mockEnqueueSnackbar,
      closeSnackbar: mockCloseSnackbar,
    }),
    enqueueSnackbar: vi.fn(),
  };
});

vi.mock('react-router', async () => {
  const actual = await vi.importActual('react-router');
  return {
    ...actual,
    useParams: vi.fn(),
    useNavigate: vi.fn(),
    useLocation: vi.fn(),
  };
});

const initialState: {
  metadata: MetadataSliceState;
  search: SearchSliceState;
  caseManager: CaseManagerSliceState;
  config: Window['config'];
  appConfig: ConfigSliceState;
  auth: {
    sessionToken: string;
    user: {
      preferredLanguage: string;
    };
  };
} = {
  metadata: {
    ...metadataInitialState,
    metadata: {
      byFolderId: {
        folder123: {
          status: 'idle',
          error: undefined,
          data: {},
        },
      },
    },
  },
  search: {
    ...searchInitialState,
    searchFiles: {
      ...searchInitialState.searchFiles,
      ungroupedSearch: {
        ...searchInitialState.searchFiles.ungroupedSearch,
        data: {
          searchMedia: {
            jsondata: {
              totalResults: {
                value: 25,
                relation: 'eq',
              },
              results: [],
              from: 0,
              to: 0,
              limit: 50,
              searchToken: '',
              timestamp: Date.now(),
            },
          },
        },
        status: 'success',
      },
    },
  },
  caseManager: caseManagerInitialState,
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    dataRegistry: {
      caseRegistry: {
        name: 'caseRegistryName123',
        id: 'caseRegistryId123',
      },
      statusRegistry: {
        name: 'statusRegistryName123',
        id: 'statusRegistryId123',
      },
      tagRegistry: {
        name: 'tagRegistryName123',
        id: 'tagRegistryId123',
      },
      evidenceTypeRegistry: {
        name: 'evidenceTypeRegistryName123',
        id: 'evidenceTypeRegistryId123',
      },
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  appConfig: {
    statusSchema: {
      status: 'complete',
      error: '',
      id: 'statusSchema123',
    },
    tagSchema: {
      status: 'complete',
      error: '',
      id: 'tagSchema123',
    },
    evidenceTypeSchema: {
      status: 'complete',
      error: '',
      id: 'evidenceTypeSchema123',
    },
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
  },
  auth: {
    sessionToken: 'sessionToken',
    user: {
      preferredLanguage: '',
    },
  },
};

describe('Case Details Table', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useParams).mockReturnValue({ selectedFolderId: 'folder123' });
    vi.mocked(useNavigate).mockReturnValue(vi.fn());
    mockEnqueueSnackbar.mockClear();
    mockCloseSnackbar.mockClear();

    vi.spyOn(GQLApi.prototype, 'getFolder').mockResolvedValue({
      id: 'folder123',
      name: 'Test Folder',
      treeObjectId: 'treeObj123',
      createdDateTime: '2025-03-20T09:00:00Z',
      modifiedDateTime: '2025-03-21T10:15:00Z',
      contentTemplates: [
        {
          id: 'template-1',
          sdo: {
            id: 'sdo-1',
            schemaId: 'schema-1',
            data: {
              createdDateTime: '2024-01-01T00:00:00Z',
              modifiedDateTime: '2024-01-01T00:00:00Z',
              caseName: 'Test Case',
              caseId: 'case-1',
              description: 'Test description',
              caseDate: '2024-01-01',
              statusId: 'Open',
              preconfiguredTagIds: ['1'],
              folderId: 'folder-456',
              createdBy: 'user-1',
              modifiedBy: 'user-1',
              createdByName: 'Test User',
            },
          },
        },
      ],
      description: 'A mocked folder for testing',
    });

    vi.spyOn(GQLApi.prototype, 'getFileMetadata').mockResolvedValue({
      temporalDataObjects: {
        offset: 0,
        limit: 1,
        count: 1,
        records: [],
      },
    });

    vi.spyOn(GQLApi.prototype, 'getBasicUserInfo').mockResolvedValue({
      id: '',
      email: '',
      lastName: '',
      firstName: '',
    });

    vi.spyOn(
      GQLApi.prototype,
      'getAllSchemaIdsByNameByOffset'
    ).mockResolvedValue(['schema-1']);

    vi.spyOn(GQLApi.prototype, 'getAllSchemaIdsByName').mockResolvedValue([
      'schema-1',
    ]);

    vi.spyOn(GQLApi.prototype, 'getEngineResults').mockResolvedValue([]);

    vi.spyOn(GQLApi.prototype, 'searchMedia').mockResolvedValue({
      searchMedia: {
        jsondata: {
          totalResults: { value: 0 },
          results: [],
          from: 0,
          to: 0,
          limit: 50,
        },
      },
    });
  });

  it('calls fetchMetadata from the search state', async () => {
    const store = configureAppStore(initialState);
    const fetchMetadataSpy = vi.spyOn(metadataSlice, 'fetchMetadata');

    render(
      <Provider store={store}>
        <MemoryRouter>
          <CaseDetailsTable />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(fetchMetadataSpy).toHaveBeenCalledWith({
        folderId: 'folder123',
        evidenceTypeSchemaId: 'evidenceTypeSchema123',
      });
    });
  });

  it('should render without crashing when case fails to load from search', async () => {
    vi.mocked(useLocation).mockReturnValue({
      pathname: '/case-manager/folder123',
      search: '',
      hash: '',
      state: { from: '/search' },
      key: 'test-key',
    });

    const storeWithFailure = configureAppStore({
      ...initialState,
      caseManager: {
        ...initialState.caseManager,
        selectedCase: {
          status: 'failure',
          data: {
            sdoId: '',
            createdDateTime: '',
            modifiedDateTime: '',
            caseId: '',
          },
        },
      },
    });

    await waitFor(() => {
      expect(() => {
        render(
          <Provider store={storeWithFailure}>
            <MemoryRouter>
              <CaseDetailsTable />
            </MemoryRouter>
          </Provider>
        );
      }).not.toThrow();
    });
  });

  it('shows unableToLoadCaseFromSearch snackbar when case fails to load from search', async () => {
    const mockNavigate = vi.fn();
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
    vi.mocked(useLocation).mockReturnValue({
      pathname: '/case-manager/folder123',
      search: '',
      hash: '',
      state: { from: '/search' },
      key: 'test-key',
    });

    const storeWithFailure = configureAppStore({
      ...initialState,
      caseManager: {
        ...initialState.caseManager,
        selectedCase: {
          status: 'failure',
          data: {
            sdoId: '',
            createdDateTime: '',
            modifiedDateTime: '',
            caseId: '',
          },
        },
      },
    });

    render(
      <Provider store={storeWithFailure}>
        <MemoryRouter>
          <CaseDetailsTable />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith({
        message: 'Unable to load case. Redirecting back to search results.',
        variant: 'warning',
      });
    });

    expect(mockNavigate).toHaveBeenCalledWith('/search');
  });
});
