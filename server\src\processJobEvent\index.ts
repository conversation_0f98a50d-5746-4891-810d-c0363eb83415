import { updateFileStatus } from '../fileStatus/updateFileStatus';
import { AppConfig } from '../types';
import { GQLApi } from '../utils/gqlApi';

export async function processJobEvent(
  jobId: string,
  appConfig: AppConfig,
  serviceToken: string
) {
  const endpoint = `${appConfig.apiRoot}/${appConfig.graphQLEndpoint}`;
  const veritoneAppId = appConfig.veritoneAppId;
  const token = serviceToken;
  let gqlApi = new GQLApi(endpoint, token, veritoneAppId);
  let job;
  try {
    job = await gqlApi.getJob(jobId);
  } catch (error) {
    console.error(
      `jobEvent - processJobEvent - failed to fetch job ${jobId}:`,
      error
    );
    throw error;
  }
  if (!job) {
    console.error(
      `jobEvent- processJobEvent - failed - jobId ${jobId} not found.`
    );
    throw new Error(`jobId ${jobId} not found.`);
  }

  const orgId = job.target.organizationId;
  if (appConfig.excludeOrgIds?.includes(orgId)) {
    console.info(
      `jobEvent - processJobEvent - skip - orgId:${orgId} is in excludeOrgIds`
    );
    return;
  }
  const roleIds = [appConfig.appEventRoleId];
  const appId = veritoneAppId;
  let orgToken;
  try {
    orgToken = await gqlApi.getApplicationJWT({
      appId,
      orgId,
      roleIds,
    });
  } catch (error) {
    console.error(
      `jobEvent- processJobEvent - failed to get org token for orgId:${orgId} tdoId:${job.target.id} job:${jobId} roleIds:${roleIds} appId:${appId}`,
      error
    );
    throw error;
  }
  if (!orgToken) {
    console.error(
      `jobEvent - processJobEvent - failed - no org token generated for orgId:${orgId} tdoId:${job.target.id} jobId:${jobId}`
    );
    throw new Error(`No org token generated for ${jobId}`);
  }
  // create a new GQLApi instance with the org token
  gqlApi = new GQLApi(endpoint, orgToken, veritoneAppId);

  console.info(
    `jobEvent - processJobEvent - processing - orgId:${orgId} tdoId:${job.target.id} jobId:${jobId}`
  );
  const result = await updateFileStatus({
    tdoId: job.target.id,
    dataRegistryId: appConfig.dataRegistry.fileStatusRegistry.id,
    dataRegistryName: appConfig.dataRegistry.fileStatusRegistry.name,
    gqlApi,
    orgId,
    logMsg: `jobEvent: ${jobId}`,
  });
  if (result.error) {
    console.error(
      `jobEvent - processJobEvent - failed - orgId:${orgId} tdoId:${job.target.id} jobId:${jobId} -  error:`,
      result.error
    );
    throw result.error;
  }

  console.info(
    `jobEvent -processJobEvent - completed - orgId:${orgId} tdoId:${job.target.id} jobId:${jobId}`
  );
  return result;
}
