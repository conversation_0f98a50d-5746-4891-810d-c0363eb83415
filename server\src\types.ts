export interface Asset {
  id: string;
  transform?: string;
  sourceData?: {
    schema?: {
      id?: string;
      dataRegistryId?: string;
    };
  };
}

export interface Task {
  id: string;
  status: string;
  modifiedDateTime: string;
  engine?: {
    id: string;
    name: string;
  };
}

export type FileStatus = 'pending' | 'processed' | 'error';

export interface JobEvent {
  jobId: string;
  timestampMs?: string;
  jobStatus?: string;
  vtn?: {
    app: string;
    eventName: string;
    type: string;
  };
}

export interface AppConfig {
  apiRoot: string;
  graphQLEndpoint: string;
  veritoneAppId: string;
  dataRegistry: {
    fileStatusRegistry: {
      name: string;
      id: string;
    };
  };
  appEventRoleId: string;
  catchupHour?: number;
  serviceToken?: string;
  port?: number;
  excludeOrgIds?: string[];
}
