# Investigate App

[//]: # ([![Lines of Code]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=ncloc&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Maintainability Rating]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=sqale_rating&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Reliability Rating]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=reliability_rating&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Security Rating]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=security_rating&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Quality Gate Status]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=alert_status&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Technical Debt]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=sqale_index&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Vulnerabilities]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=vulnerabilities&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Coverage]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=coverage&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Code Smells]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=code_smells&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Duplicated Lines &#40;%&#41;]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=duplicated_lines_density&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![Bugs]&#40;https://sonarcloud.io/api/project_badges/measure?project=investigate-app&metric=bugs&token=7d766c19032706ddb69217fbc9dd466846d8a39f&#41;]&#40;https://sonarcloud.io/dashboard?id=investigate-app&#41;)

[//]: # ([![]&#40;https://data.jsdelivr.com/v1/package/npm/aiware-js/badge&#41;]&#40;https://www.jsdelivr.com/package/npm/aiware-js&#41;)

AI that Helps Your People Solve Investigations Faster With A Central Evidence Hub

### How to start client:
Make sure `./client/config.json` has the correct configuration before running.
```
cd client 
yarn 
yarn start:dev
```

© Veritone, 2025
