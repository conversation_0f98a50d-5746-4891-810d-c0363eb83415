{">": ">", "active": "Active", "add": "Add", "addFiles": "Add Files", "addNewCase": "Create New Case", "addNewStatus": "Add New Status", "addNewTag": "Add New Tag", "addStatus": "Add Status", "addTag": "Add Tag", "addTags": "Add Tags", "addToShare": "Add to Share", "advancedSearch": "Advanced Search", "areYouSure": "Are you sure?!", "areYouSureCloseAndLoseData": "Are you sure you want to close the panel and lose your data?", "back": "Back", "backToCaseManager": "Back to Case Manager", "belowYouCanFindKeySettings": "Below you can find key settings that can be managed for your application.", "cancel": "Cancel", "case": "Case", "caseFiles": "Case Files", "caseCreatedSuccessfully": "Case Created Successfully!", "caseCreationFailed": "Case Creation Failed!", "caseDate": "Case Date", "caseDateWithColons": "Case Date:", "caseID": "Case ID", "caseIDWithAsterisk": "Case ID *", "caseIdWithColons": "Case ID: ", "caseInformation": "Case Information", "caseManagement": "Case Management", "caseManager": "Case Manager", "caseName": "Case Name", "caseNameWithAsterisk": "Case Name *", "caseOwner": "Case Owner", "caseOwnerWithColons": "Case Owner:", "caseRetentionPolicy": "Case Retention Policy", "caseStatus": "Case Status", "chooseACase": "Choose a Case", "clearAll": "Clear All", "clearStatus": "Clear Status Only", "close": "Close", "closePanel": "Close Panel", "cognition": "Cognition", "confirmClosePanel": "Confirm Close Panel", "createCase": "Create Case", "createNewCase": "Create New Case", "createNewShare": "Create New Share", "dashboard": "Dashboard", "clear": "Clear", "deleteAndReAssign": "Re-Assign & Delete", "deleteAndReAssignStatusDescription": "There are cases using this label, clear the status{br}or choose a new status to assign them too", "deleteCaseConfirmationMsg": "Deleting a case removes all files and data and cannot be undone", "confirmByTyping": "Confirm by typing", "below": "below.", "deleteFilesConfirmationMsg": "Deleting will immediately remove content from your Organization. They can be recovered up to a period of 30 days.", "deleteFile": "delete-file", "everyOneInOrgWillLoseAccess": "Everyone in the Organization will lose access", "theFileWillBeDeleted": "The file will be deleted", "allSelectedFilesWillBeDeleted": "All selected content will be deleted", "deleteOnlyStatusDescription": "There are cases using this label, choose a new{br}status to assign them too", "deleteStatus": "Delete Status Label", "deleteStatusDescription": "Are you sure you want to delete this status{br}label? This action cannot be undone.", "deleteTag": "Delete Tag", "deleteTagDescription": "Are you sure you want to delete the {tag} tag?", "deleteTags": "Delete Tags", "deleteTagsDescription": "Are you sure you want to delete the selected tags?", "description": "Description", "editCase": "Edit Case", "editRetention": "Edit Retention", "editStatusLabels": "Edit Status Labels", "editTags": "Edit Tags", "evidenceData": "Evidence Data", "evidenceType": "Evidence Type", "evidenceTypesWithColon": "Evidence Types:", "fileOwner": "File Owner", "fileStatus": "File Status", "fileUploader": "File Uploader", "goToNewCase": "Go to New Case", "inactive": "Inactive", "keyword": "Keyword", "move": "Move", "nameStatusAndAssignColor": "Name your status and assign a color value.", "nameTagAndChooseVisibility": "Add up to ten tags at once. Tags have a max character count of fifteen. Separate tags with a comma.", "noCaseSelected": "No case selected", "noCasesFoundDescription": "You haven't created any cases. Click the Add New Case button to get started.", "noFilesFound": "No Case Files found", "noFilesFoundDescription": "Upload files to your case to view cognition{br}results and share findings.", "nothingFound": "Nothing Found", "overview": "Overview", "permissions": "Permissions", "pleaseSelectCase": "Choose a case from the table to view its details here.", "setVisibility": "Set Visibility", "setColor": "Set Color", "presetColors": "Preset Colors:", "color": "Color", "resetAll": "Reset All", "resetFilters": "Reset Filters", "retentionDate": "Retention Date", "save": "Save", "saveChanges": "Save Changes", "search": "Search", "selectAStatus": "Select a status", "selectStoredLocation": "Select the location where this content will be stored.", "emptyState": "Empty State", "settings": "Settings", "share": "Share", "shareCase": "Share Case", "shares": "Shares", "someThingWentWrong": "Something went wrong...", "statusName": "Status Name", "statuses": "Statuses", "tagName": "Tag Name(s)", "tags": "Tags", "unableToLoadData": "Data was not found or it was unable to load.{br}Try refreshing this page or try back later.", "unableToUploadFile": "Unable to upload file", "uploadFileSuccess": "File {name} uploaded successfully", "refreshPage": "Refresh the page and try again", "summary": "Summary", "NoResults": "No Results", "NoEngineRun": "No Engine Run", "uploadDate": "Upload Date", "uploadFile(s)": "Upload File(s)", "uploadFiles": "Upload Files", "caseDetail": "Case Detail", "viewCaseDetails": "View Case Details", "viewShareDetails": "View Share Details", "yesDeleteCase": "Yes, Delete Case", "youAreAboutDeleteCase": "You are about to delete the selected case.", "youCanStartAddingFiles": "You can start adding files and case information.", "selectAFolder": "Move to Case", "chooseCase": "Choose an active case to move file(s) to.", "chooseCaseAddFiles": "Choose an active case to add file(s) to.", "selectACasePlaceholder": "Select a Case", "casePendingDelete": "This case is pending deletion and cannot be selected.", "caseDeletedSuccessfully": "Case Deletion in progress. It will be removed shortly.", "caseDeletionFailed": "Case Deletion Failed!", "caseUpdatedSuccess": "Case Updated Successfully!", "caseUpdateFailed": "Case Update Failed!", "searchPage": "Search and Manage Files", "fileType": "File Type", "defaultEmpty": "--", "wordsInTranscription": "Words in Transcription", "faceDetections": "Face Detections", "objectDescriptors": "Object Descriptors", "vehicleRecognition": "Vehicle Recognition", "licensePlateRecognition": "License Plate Recognition", "sceneClassification": "Scene Classification", "textRecognition": "Text Recognition", "callRecording": "911 Call Recording", "arrestReport": "Arrest Report", "bodyWornCamera": "Body Worn Camera", "bookingPhoto": "Booking Photo", "citizenSubmittedVideo": "Citizen Submitted Video", "crimeScenePhoto": "Crime Scene Photo", "inCarVideo": "In Car Video", "interviewAudioRecording": "Interview Audio Recording", "interviewRoomRecording": "Interview Room Recording", "mobileDeviceExtraction": "Mobile Device Extraction", "securityCameraVideo": "Security Camera Video", "video": "Video", "audio": "Audio", "document": "Document", "image": "Image", "title": "Title", "noResultsFound": "No Results Found", "refineAndTryAgain": "Refine your search term and try again.", "confirmDiscardChanges": "Are you sure you want to discard your changes?", "addStatusConfirm": "Add Status Confirmation", "addTagConfirm": "Add Tag Confirmation", "wouldYouLikeToSaveYourChanges": "Would you like to save your changes?", "wouldYouLikeToSaveYourChangesBeforeClosing": "Would you like to save your changes before closing?", "selectedTagsWillAllBeSetToThisOption": "Selected tags will all be set to this option.", "selectedStatusesWillAllBeSetToThisOption": "Selected statuses will all be set to this option.", "setTagVisibility": "Set Tag Visibility", "setStatusVisibility": "Set Status Visibility", "fileName": "File Name", "filename": "File Name", "sourceName": "Source Name", "caseId": "Case ID", "dateUploaded": "Date Uploaded", "selectCaseTags": "Select up to 10 case tags", "tagsSelected": "Tags Selected", "filePendingDelete": "This file is pending deletion and cannot be selected.", "fileDeletedSuccessfully": "File Deletion in progress. It will be removed shortly.", "fileDeletionFailed": "File Deletion Failed.", "yesDeleteFile": "Yes, Delete File", "yesDeleteFiles": "Yes, Delete Selected File(s)", "deleteFileConfirmationMsg": "Deleting a file cannot be undone.", "viewCase": "View Case", "uploaded": "Uploaded", "uploadedWithColons": "Uploaded:", "viewFile": "View File", "addToCase": "Add to Case", "moveToAnotherCase": "Move to Another Case", "selected": "Selected", "file": "File", "files": "Files", "addTagSuccess": "Tag added successfully", "addTagFailure": "Failed to add tag", "saveTagsSuccess": "Tags saved successfully", "saveTagsFailure": "Failed to save tags", "addStatusSuccess": "Status added successfully", "addStatusFailure": "Failed to add status", "saveStatusesSuccess": "Statuses saved successfully", "saveStatusesFailure": "Failed to save statuses", "filters": "Filters", "reset": "Reset", "status": "Status", "apply": "Apply", "showAll": "Show All", "allowedLetters": "Only letters, numbers, _ , and - allowed", "saveStatusesConcurrentModificationError": "Statuses were modified by another user. You may need to refresh the page.", "saveTagsConcurrentModificationError": "Tags were modified by another user. You may need to refresh the page.", "dataModified": "Data Modified", "discardChanges": "Discard Changes", "discard": "Discard", "saveModifiedStatusesMessage": "Statuses were modified by another user. You can 'Discard' your changes or 'Save', potentially overwriting their data.", "saveModifiedTagsMessage": "Tags were modified by another user. You can 'Discard' your changes or 'Save', potentially overwriting their data.", "fileMovedSuccess": "File moved successfully", "invalidCharacterErrorMessage": "Invalid character only \"-\" and \"_\" are allowed.", "statusLabelNameAlreadyTaken": "Status label name already taken.", "editMetadata": "<PERSON>", "editMetadataWarning": "Any changes to asset metadatas are permanent and can not be undone.", "fileSize": "File Size", "fileFormat": "File Format", "fileId": "File ID", "duration": "Duration", "source": "Source", "confirmSaveChanges": "Changes Confirmation", "fileNameRequired": "File Name is required", "caseNameRequired": "Case Name is required", "showTotalResults": "Showing {total} results", "sortCategory": "Sort Category : ", "fileNameAZ": "File Name A-Z", "fileNameZA": "File Name Z-A", "newestToOldest": "Newest to Oldest", "oldestToNewest": "Oldest to Newest", "view": "View :", "grouped": "Grouped", "unGrouped": "Un-Grouped", "showAllCategories": "Show All Categories", "hideIfNoResults": "Hide if no results", "transcription": "Transcription", "faceRecognition": "Facial Recognition", "objectDetection": "Object Detection", "metadata": "<PERSON><PERSON><PERSON>", "filterSearchWarning": "Keyword searches can take a bit to load.", "no": "No", "found": "Found", "reAssignTo": "Re-Assign to:", "noOptionsLeft": "No options left!", "noDataAvailable": "No data available", "lightMode": "Light Mode", "darkMode": "Dark Mode", "space": " ", "blurImages": "Blur Images", "all": "All", "none": "None", "results": "Results", "cadId": "CAD ID", "callerPhoneNumber": "Caller Phone Number", "reportNumber": "Report Number", "officerName": "Officer Name", "badgeId": "Badge ID", "deviceId": "Device ID", "cameraPhysicalLocation": "Camera Physical Location", "cameraPhysicalAddress": "Camera Physical Address", "locationTimeline": "Location Timeline", "lastName": "Last Name", "firstName": "First Name", "dateOfBirth": "Date of Birth", "citizenName": "Citizen Name", "cameraType": "Camera Type", "evidenceTechnician": "Evidence Technician", "unitNumber": "Unit Number", "interviewer": "Interviewer", "interviewee": "Interviewee", "interviewRoom": "Interview Room", "deviceName": "Device Name", "deviceType": "Device Type", "deviceModel": "Device Model", "deviceRegisteredOwner": "<PERSON>ce Registered Owner", "cameraFacingDirection": "Camera Facing Direction", "metadataUpdated": "Metadata Updated", "assetStatus": "Asset Status", "createdBy": "Created By", "contentType": "Content Type", "contentTypesWithColon": "Content Types:", "failedToUpdateMetadata": "Failed to update metadata", "case-evidence-list-item": "{type} ({count})", "enteredMoreThanTenTags": "You entered more than 10 tags. Please enter up to 10 tags.", "invalidCharacterInTag": "Invalid character in {tag}. Only letters, numbers, _ , and - allowed.", "tagNameIsTooLong": "Tag {tag} is too long. Please enter a tag with a maximum of 15 characters.", "tagNameAlreadyTaken": "The following tags already exist: {tags}", "foundString": "Results", "foundFullTextString": "Plain text", "libraries": "Libraries", "entities": "Entities", "getLibrariesFailed": "Failed to get libraries", "getEntitiesFailed": "Failed to get entities", "startExploring": "Start Exploring", "searchForAnything": "Search for anything. We'll help you find it.", "invalidCase": "Case has been deleted or does not exist. Please select another case.", "noneOperatorWarning": "Please select an operator before selecting a new one", "somethingWrongSearch": "Something went wrong in your search request. {gqlRequestId}", "selectACategory": "Select a category to start", "clickTheCategoryWheel": "Click the category wheel to choose search categories.", "shareName": "Share Name", "message": "Message", "expirationDate": "Expiration Date", "recipients": "Recipients", "addRecipient": "Add Recipient", "youAreAboutToShareSensitiveContent": "You are about to share sensitive content and files with these recipients", "emailAddresses": "Email Addresses", "createShare": "Create Share", "createShareSubtitle": "Select which files you want the recipients to have access to and choose when the share expires.", "shareNameRequired": "Share Name is required", "messageRequired": "Message is required", "expirationDateRequired": "Expiration Date is required", "emailRequired": "Email is required", "firstNameRequired": "First Name is required", "lastNameRequired": "Last Name is required", "expirationDateNotGreaterThanNow": "Expiration Date must be greater than the current date", "emailBadFormat": "Email is not a valid format", "metadataCouldNotBeFound": "<PERSON><PERSON><PERSON> could not be found", "failedToRetrieveMetadata": "Failed to retrieve metadata", "noRootFolder": "No root folder is created. Please contact your administrator.", "removeOperator": "Remove Operator", "invalidExpression": "Invalid Expression", "lackOfOpenParenthesis": "Lack of open parenthesis", "lackOfCloseParenthesis": "Lack of close parenthesis", "redundantParenthesis": "Redundant parenthesis", "loading": "Loading...", "cognitionFiltersNotAvailable": "Face and object search requires cognition libraries to exist in the organization.", "failedGetCaseSchemaId": "Failed to get case schema ID", "failedGetStatusSchemaId": "Failed to get status schema ID", "failedGetTagSchemaId": "Failed to get tag schema ID", "failedGetEvidenceTypeSchemaId": "Failed to get evidenceType schema ID", "failedGetInvestigatePermissionSet": "Failed to get Investigate permission set", "noStatus": "No Status", "caseTags": "Case Tags", "noCasesPleaseCreateACase": "No cases found. Please create a case to get started.", "caseTagSettings": "Case Tag Settings", "dateCreated": "Date Created", "password": "Password (Optional)", "showPassword": "Show Password", "rowPerPage": "Rows per page", "visibility": "Visibility", "caseCreateDescriptionCharCount": "{length}/1,000", "shareManager": "Share Manager", "sharedBy": "Shared by:", "fileCount": "# of Files", "recipientCount": "# of Recipients", "expDate": "Exp. Date", "deleteShareConfirmationMsg": "Deleting a share and all of its files cannot be undone.", "yesDeleteShare": "Yes, Delete", "access": "Access", "userGrantedAccess": "User granted access to this share", "addUser": "Add User", "additionalInformation": "Additional Information", "expirationDateWithColons": "Expiration Date:", "sharedDateWithColons": "Shared Date:", "numberOfFiles": "Number of Files:", "emailAddress": "Email Address", "sendButton": "Send", "resendShare": "Resend Share", "removeUser": "Remove User", "changeShareExpirationDate": "Change Share Expiration Date", "fileNotInCase": "The file is not currently in a case. Please move this file to a case.", "caseFile": "Case File", "filesShared": "{count} Files shared", "shareOverview": "Share Overview", "backToShareManager": "Back to Share Manager", "shareDetail": "Share Details", "download": "Download", "delete": "Delete", "sendToRedact": "Send to Redact", "sendToTrack": "Send to Track", "trackProcessingHasNotBeenExecuted": "Track processing has not been executed for this file.", "trackAppNotAvailableForUser": "Track app is not available for this user.", "somethingWentWrongWhileOpeningInTrack": "Something went wrong while opening in Track.", "trackEngineJobFailed": "Track engine job failed.", "trackEngineIsProcessing": "Track engine is still processing.", "manageWhoHasPermissionsForThisCase": "Manage who has the permissions to access this case.", "managePermissions": "Manage Permissions", "grantAccessForCaseToPeopleOrGroups": "Grant access for this case to specific people or groups.", "addPeopleOrGroups": "Add People or Groups", "people": "People", "groups": "Groups", "noUsersFound": "No users found", "noGroupsFound": "No groups found", "searchForUsersOrGroups": "Search for users or groups to add.", "theseUsersHaveAccessToThisGroupsPermissions": "These users have access to this group's permissions.", "searchForPeopleInThisGroup": "Search for people in this group", "remove": "Remove", "membersCount": "{count} members", "fileProperties": "File Properties", "details": "Details", "dateModified": "Date Modified", "mimeType": "MIME Type", "size": "Size", "uploadedBy": "Uploaded By", "loadingSubtitleForSearch": "Keyword searches are slower - they scan all fields.", "errorFetchMetadata": "Error fetching metadata", "unableToLoadCaseFromSearch": "Unable to load case. Redirecting back to search results.", "done": "Done", "permissionSetViewer": "Viewer", "permissionSetEditor": "Editor"}