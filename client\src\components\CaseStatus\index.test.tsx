import { configureAppStore } from '@store/index';
import {
  CaseManagerSliceState,
  initialState as caseManagerInitialState,
} from '@store/modules/caseManager/slice';
import {
  SettingsSliceState,
  initialState as settingsInitialState,
} from '@store/modules/settings/slice';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { describe, expect, it, vi } from 'vitest';
import CaseStatusMenu from '.';
import { render } from '../../../test/render';

const initialStateForMock: {
  settings: SettingsSliceState;
  caseManager: CaseManagerSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  settings: {
    ...settingsInitialState,
    fetchedStatuses: {
      status: 'complete',
      error: '',
      statuses: [
        { id: 'key1', label: 'Activated', color: '#0000FF', active: true },
        {
          id: 'key2',
          label: 'Deactivated',
          color: '#0000FF',
          active: true,
        },
        { id: 'key3', label: 'Suspended', color: '#0000FF', active: true },
        {
          id: 'key4',
          label: 'In progress',
          color: '#0000FF',
          active: false,
        },
        { id: 'key5', label: 'Closed', color: '#0000FF', active: false },
        { id: 'key6', label: 'Deleted', color: '#0000FF', active: true },
      ],
      sdoId: '',
    },
  },
  caseManager: {
    ...caseManagerInitialState,
    rootFolder: {
      status: 'idle',
      error: '',
      id: 'rootFolder123',
    },
    folderContentTemplateSchema: {
      status: 'idle',
      error: '',
      id: 'folderContentTemplateSchemaId123',
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    dataRegistry: {
      caseRegistry: {
        name: 'caseRegistryName123',
        id: 'caseRegistryId123',
      },
      statusRegistry: {
        name: 'statusRegistryName123',
        id: 'statusRegistryId123',
      },
      tagRegistry: {
        name: 'tagRegistryName123',
        id: 'tagRegistryId123',
      },
      evidenceTypeRegistry: {
        name: 'evidenceTypeRegistryName123',
        id: 'evidenceTypeRegistryId123',
      },
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

describe('Case Status Menu', () => {
  it('open status menu, render correct check mark in current status', async () => {
    const mockOnSaveChange = vi.fn();
    const store = configureAppStore(initialStateForMock);
    render(
      <Provider store={store}>
        <CaseStatusMenu
          currentStatusId="key1"
          onSaveStatus={mockOnSaveChange}
          currentRowId={'rowId123'}
        />
      </Provider>
    );

    expect(screen.getByTestId('case-status')).toBeInTheDocument();

    userEvent.click(screen.getByTestId('case-status-button'));

    await waitFor(() => {
      expect(screen.getByTestId('case-status-menu')).toBeInTheDocument();
    });

    expect(screen.getByTestId('check-icon-key1')).toBeInTheDocument();
  });

  it('select status should update case sdo and cancel polling', async () => {
    const mockOnSaveChange = vi.fn((statusId) => {
      pollPromiseRef.current?.abort();
      mockOnSaveChange.mock.calls.push(statusId);
    });
    const mockAbort = vi.fn();
    const store = configureAppStore(initialStateForMock);
    const pollPromiseRef = { current: { abort: mockAbort } };

    render(
      <Provider store={store}>
        <CaseStatusMenu
          currentStatusId="key1"
          onSaveStatus={(statusId) => {
            mockOnSaveChange(statusId);
          }}
          currentRowId={'rowId123'}
        />
      </Provider>
    );

    expect(screen.getByTestId('case-status')).toBeInTheDocument();

    fireEvent.click(screen.getByTestId('case-status-button'));

    await waitFor(() => {
      expect(screen.getByTestId('case-status-menu')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('case-status-item-key2'));

    await waitFor(() => {
      expect(mockAbort).toHaveBeenCalled();
      expect(mockOnSaveChange).toHaveBeenCalledWith('key2');
    });
  });
});
