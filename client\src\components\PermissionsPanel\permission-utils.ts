import {
  AuthGroup,
  PermissionSet,
  ACLRecordType,
  MemberType,
  User,
} from '@utils/olp/types.ts';
import { userOrGroupOption } from '@components/PermissionsPanel/index.tsx';

interface authGroupMemberWithPermissionSet {
  member: AuthGroup;
  permissionSet?: PermissionSet;
  folderId: string;
}

interface userMemberWithPermissionSet {
  member: User;
  permissionSet?: PermissionSet;
  folderId: string;
}

const getUserMembers = (caseUsersPermissionData: ACLRecordType[]) => {
  const caseUserMembers: userMemberWithPermissionSet[] = [];

  caseUsersPermissionData.forEach((userOrGroupRecord) => {
    if (
      userOrGroupRecord.member.memberType === MemberType.User &&
      userOrGroupRecord.member
    ) {
      // return userOrGroupRecord.member;
      caseUserMembers.push({
        member: userOrGroupRecord.member as User,
        permissionSet: userOrGroupRecord.permissionSet,
        folderId: userOrGroupRecord.id,
      });
    }
  });

  return caseUserMembers;
};

// Un-permission users are those that are in allOrgUsersOrGroups but not in casePermissionData
const getUnpermissionedUsers = (
  casePermissionData: ACLRecordType[],
  allOrgUsers: Array<User>
) => {
  const casePermissionUsers: ACLRecordType[] =
    getCasePermissionedUsers(casePermissionData);

  const caseUserMembers: userMemberWithPermissionSet[] =
    getUserMembers(casePermissionUsers);

  const allOrgUserMembers: userMemberWithPermissionSet[] = allOrgUsers.map(
    (orgUser) => ({
      member: orgUser,
      folderId: orgUser.id,
    })
  );

  // filter out those users that are the same in both allOrg and casePermissionData
  const unpermissionedUsers = allOrgUserMembers.filter(
    (user) =>
      !caseUserMembers.some((caseUser) => {
        if (!caseUser) {
          return false;
        }
        if (!user) {
          return false;
        }
        return user.member.id === caseUser.member.id;
      })
  );

  return unpermissionedUsers;
};

const getGroupMembers = (casePermissionData: ACLRecordType[]) => {
  const caseGroupMembers: authGroupMemberWithPermissionSet[] = [];

  casePermissionData.forEach((userOrGroupRecord) => {
    if (
      userOrGroupRecord.member.memberType === MemberType.Group &&
      userOrGroupRecord.member
    ) {
      // return userOrGroupRecord.member;
      caseGroupMembers.push({
        member: userOrGroupRecord.member as AuthGroup,
        permissionSet: userOrGroupRecord.permissionSet,
        folderId: userOrGroupRecord.id,
      });
    }
  });

  return caseGroupMembers;
};

const getUnpermissionedGroups = (
  casePermissionData: ACLRecordType[],
  allOrgGroups: Array<AuthGroup>
) => {
  // get members for both groups
  const caseGroupMembers: authGroupMemberWithPermissionSet[] =
    getGroupMembers(casePermissionData);

  const allOrgGroupMembers: authGroupMemberWithPermissionSet[] =
    allOrgGroups.map((group: AuthGroup) => ({
      member: group,
      folderId: group.id,
    }));

  // filter out those groups that are the same in both allOrg and casePermissionData
  const unpermissionedGroups = allOrgGroupMembers.filter(
    (group) =>
      !caseGroupMembers.some((e) => {
        if (!e) {
          return false;
        }
        if (!group) {
          return false;
        }
        return group.member.id === e.member.id;
      })
  );

  return unpermissionedGroups;
};

// This function returns a list of users and groups that are not already
// permission-ed for the current folder being edited.
export const getNonPermissionedUsersAndGroups = (
  casePermissionData: ACLRecordType[],
  allOrgGroups: Array<AuthGroup>,
  allOrgUsers: Array<User>
) => {
  const unpermissionedGroups = getUnpermissionedGroups(
    casePermissionData,
    allOrgGroups
  );

  const membersOfGroups: User[] = [];

  unpermissionedGroups.forEach((group: authGroupMemberWithPermissionSet) => {
    if (group.member.members && group.member.members.records) {
      const groupUsers = group.member.members.records.filter(
        (record) => record.member !== null
      ) as unknown as Array<{ member: User }>;

      groupUsers.map((record) => {
        membersOfGroups.push({
          id: record.member.id,
          firstName: record.member.firstName,
          lastName: record.member.lastName,
          email: record.member.email,
          memberType: MemberType.User,
          objectMemberType: record.member.objectMemberType,
        });
      });
    }
  });

  const unpermissionedUsers = getUnpermissionedUsers(
    casePermissionData,
    allOrgUsers
  );

  const combinedResults: userOrGroupOption[] = [
    ...unpermissionedUsers.map((user) => ({
      id: user.folderId,
      memberId: user.member.id,
      firstName: user.member.firstName,
      lastName: user.member.lastName,
      name: `${user.member.firstName} ${user.member.lastName}`,
      email: user.member.email,
      memberType: user.member.memberType,
      permissionSet: user.permissionSet,
    })),
    ...unpermissionedGroups.map((group) => ({
      id: group.folderId,
      memberId: group.member.id,
      name: group.member.name,
      memberType: group.member.memberType,
      count: group.member.members?.count,
      permissionSet: group.permissionSet,
      members: membersOfGroups as unknown as User[],
    })),
  ];

  return combinedResults;
};

// NOTE: In the back-end, permission-ed users have their own private group! That
// is, upon adding a user to a case, the back-end creates a private group just
// for that user.
// This function gleans those users and creates 'proper' ACLRecords of type User
const getCasePermissionedUsers = (casePermissionData: ACLRecordType[]) => {
  const allAuthGroups = casePermissionData.filter(
    (userOrGroupRecord) =>
      userOrGroupRecord.member.memberType === MemberType.Group
  );

  const casePermissionedUsers: ACLRecordType[] = [];

  allAuthGroups.forEach((group) => {
    const groupsMember = group.member as AuthGroup;

    if (
      group.member.name
        ?.toLowerCase()
        .includes('default private group for user') &&
      groupsMember.members.count === 1
    ) {
      const userRecord = {
        id: group.id,
        permissionSet: group.permissionSet,
        member: groupsMember.members.records[0].member,
        objectID: group.objectID,
        objectType: group.objectType,
        createdAt: group.createdAt,
      } as ACLRecordType;
      casePermissionedUsers.push(userRecord);
    }
  });

  return casePermissionedUsers;
};

const getPermissionedUsers = (
  casePermissionData: ACLRecordType[],
  allOrgUsers: Array<User>
) => {
  const casePermissionnUsers: ACLRecordType[] =
    getCasePermissionedUsers(casePermissionData);

  const caseUserMembers: userMemberWithPermissionSet[] =
    getUserMembers(casePermissionnUsers);

  const allOrgUserMembers: userMemberWithPermissionSet[] = allOrgUsers.map(
    (orgUser) => ({
      member: orgUser,
      folderId: orgUser.id,
    })
  );

  // filter out those groups that are the same in both allOrg and casePermissionData
  const permissionedUsers = caseUserMembers.filter((group) =>
    allOrgUserMembers.some((e) => {
      if (!e) {
        return false;
      }
      if (!group) {
        return false;
      }
      return group.member.id !== e.member.id;
    })
  );

  return permissionedUsers;
};

const getPermissionedGroups = (
  casePermissionData: ACLRecordType[],
  allOrgGroups: Array<AuthGroup>
) => {
  // get members for both groups, those that are permission-ed for the case and
  // those that are in the org
  const caseGroupMembers: authGroupMemberWithPermissionSet[] =
    getGroupMembers(casePermissionData);

  const allOrgGroupMembers: authGroupMemberWithPermissionSet[] =
    allOrgGroups.map((group: AuthGroup) => ({
      member: group,
      folderId: group.id,
    }));

  // filter out those groups that are the same in both allOrg and casePermissionData
  // to get the permission-ed groups
  const permissionedGroups = caseGroupMembers.filter((group) =>
    allOrgGroupMembers.some((e) => {
      if (!e) {
        return false;
      }
      if (!group) {
        return false;
      }
      return group.member.id === e.member.id;
    })
  );

  return permissionedGroups;
};

export const getPermissionedUserAndGroups = (
  casePermissionData: ACLRecordType[],
  allOrgGroups: Array<AuthGroup>,
  allOrgUsers: Array<User>
) => {
  const permissionedGroups = getPermissionedGroups(
    casePermissionData,
    allOrgGroups
  );

  let membersOfGroups: User[] = [];
  const groupMemberUsers: Array<{ id: string; members: Array<User> }> = [];

  // Make a list (membersOfGroups) of all the users in each group
  permissionedGroups.forEach((group: authGroupMemberWithPermissionSet) => {
    membersOfGroups = [];
    if (group.member.members && group.member.members.records) {
      const groupUsers = group.member.members.records.filter(
        (record) => record.member !== null
      ) as unknown as Array<{ member: User }>;

      groupUsers.map((record) => {
        membersOfGroups.push({
          id: record.member.id,
          firstName: record.member.firstName,
          lastName: record.member.lastName,
          email: record.member.email,
          memberType: MemberType.User,
          objectMemberType: record.member.objectMemberType,
        });
      });
      groupMemberUsers.push({ id: group.member.id, members: membersOfGroups });
    }
  });

  const permissionedUsers = getPermissionedUsers(
    casePermissionData,
    allOrgUsers
  );

  // Create a list of userORGroupOption that combines both users and groups
  const combinedResults: userOrGroupOption[] = [
    ...permissionedUsers.map((user) => ({
      id: user.folderId,
      memberId: user.member.id,
      firstName: user.member.firstName,
      lastName: user.member.lastName,
      name: `${user.member.firstName} ${user.member.lastName}`,
      email: user.member.email,
      memberType: user.member.memberType,
      permissionSet: user.permissionSet,
    })),
    ...permissionedGroups.map((group) => ({
      id: group.folderId,
      memberId: group.member.id,
      name: group.member.name,
      memberType: group.member.memberType,
      count: group.member.members?.records.length,
      permissionSet: group.permissionSet,
      members: groupMemberUsers.find(
        (gMemberUser) => gMemberUser.id === group.member.id
      )?.members,
    })),
  ];

  return combinedResults;
};
