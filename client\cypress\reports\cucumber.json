[{"description": "", "elements": [{"description": "", "id": "case-manager;delete-created-cases-after-run", "keyword": "<PERSON><PERSON><PERSON>", "line": 334, "name": "Delete created cases after run", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 2072000000}}, {"arguments": [], "keyword": "Given ", "line": 4, "name": "The user is on Case Management screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:15"}, "result": {"status": "passed", "duration": 18703000000}}, {"arguments": [{"rows": [{"cells": ["caseId"]}, {"cells": ["Cypress Test Case 1"]}, {"cells": ["Cypress Test Case 2"]}, {"cells": ["Cypress Test Case Upload 1"]}, {"cells": ["Cypress Test Case Edit <PERSON>"]}, {"cells": ["Cypress Test Case Edit Kebab 2"]}, {"cells": ["Cypress Test Case Edit Detail"]}, {"cells": ["Cypress Test Case Edit Detail 2"]}, {"cells": ["Cypress Test Case Delete"]}, {"cells": ["Cypress Test Filtered"]}, {"cells": ["000 Cypress Test Moved File"]}, {"cells": ["Cypress Test Move File"]}, {"cells": ["Cypress Test sort table"]}, {"cells": ["Cypress Test Move File 1"]}, {"cells": ["Cypress Test Case Upload Delete 1"]}, {"cells": ["Cypress Test Update status table"]}, {"cells": ["Cypress Test Update status detail"]}, {"cells": ["Test Upload a file in an empty case"]}, {"cells": ["Test Upload a file to an empty case"]}, {"cells": ["Cypress Test Keyboard Navigation"]}]}], "keyword": "Given ", "line": 335, "name": "The user deletes the following cases if exist:", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:80"}, "result": {"status": "passed", "duration": 77745000000}}], "tags": [{"name": "@e2e", "line": 333}, {"name": "@case-manager", "line": 333}], "type": "scenario"}, {"description": "", "id": "case-manager;delete-sdos-and-folders-after-run", "keyword": "<PERSON><PERSON><PERSON>", "line": 358, "name": "Delete SDOs and folders after run", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 1285000000}}, {"arguments": [], "keyword": "Given ", "line": 4, "name": "The user is on Case Management screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:15"}, "result": {"status": "passed", "duration": 21687000000}}, {"arguments": [{"rows": [{"cells": ["folderName"]}, {"cells": ["Cypress Test Case 1"]}, {"cells": ["Cypress Test Case 2"]}, {"cells": ["Cypress Test Case Upload 1"]}, {"cells": ["Cypress Test Case Edit <PERSON>"]}, {"cells": ["Cypress Test Case Edit Kebab 2"]}, {"cells": ["Cypress Test Case Edit Detail"]}, {"cells": ["Cypress Test Case Edit Detail 2"]}, {"cells": ["Cypress Test Case Delete"]}, {"cells": ["Cypress Test Filtered"]}, {"cells": ["000 Cypress Test Moved File"]}, {"cells": ["Cypress Test Move File"]}, {"cells": ["Cypress Test sort table"]}, {"cells": ["Cypress Test Move File 1"]}, {"cells": ["Cypress Test Case Upload Delete 1"]}, {"cells": ["Cypress Test Update status table"]}, {"cells": ["Cypress Test Update status detail"]}, {"cells": ["Test Upload a file in an empty case"]}, {"cells": ["Test Upload a file to an empty case"]}, {"cells": ["Cypress Test Keyboard Navigation"]}, {"cells": ["Cypress Test Case No Status"]}]}], "keyword": "Given ", "line": 359, "name": "The user deletes the following SDOs and folders if exist:", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:275"}, "result": {"status": "passed", "duration": 19816000000}}], "tags": [{"name": "@e2e", "line": 357}, {"name": "@case-manager", "line": 357}], "type": "scenario"}], "id": "case-manager", "line": 1, "keyword": "Feature", "name": "Case Manager", "tags": [], "uri": "cypress\\e2e\\features\\case-manager.feature"}]