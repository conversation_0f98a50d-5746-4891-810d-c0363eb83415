.permissions-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;

  .permissions-panel__title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
    background-color: var(--background-primary);
  }

  .permissions-panel__heading-title {
    font-size: 18px;
    font-weight: 600;
    margin-left: 30px;
    color: var(--text-primary)
  }

  .permissions-panel__main-container {
    flex: 1;
    padding: 0 30px 0 30px;
    background-color: var(--background-primary);
  }

  .permissions-panel__vertical-field-gap {
    margin-top: 27px;
    position: relative;
  }

  .permissions-panel__subtitle-text {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 400;
  }

  .permissions-panel__tabs-container {
    border-bottom: 1px solid var(--app-status-tab-border);
    .permissions-panel__tab {
      color: var(--text-primary);
      background: var(--background-primary);

      &.selected {
        background: var(--background-primary);
        color: var(--text-primary);
      }
    }
  }

  .permissions-panel__added-user {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    flex: 2 1 auto;
    margin-top: 20px;
    width: 105%;

    .permissions-panel__add-user-email {
      margin-top: 3px;
      font-size: 12px;
      font-weight: 200;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .permissions-panel__added-user-group-icon {
      width: 5%
    }

    .permissions-panel__added-user-name {
      font-size: 14px;
      color: var(--text-primary);
      width: 50%;
      margin-left: 12px;
    }

    .permissions-panel__added-user-select-container {
      height: 36px;
      width: 20%;
    }

    .permissions-panel__added-user-select {
      height: 36px;
      width: 100%;
    }

    .permissions-panel__added-user-group-menu-item {
        color: var(--text-primary);
        font-size: 14px;
        padding: 10px 20px;
        background-color: var(--background-primary);

        &:hover {
            background-color: var(--menu-background-hover);
        }
    }

    .permissions-panel__added-user-remove {
      cursor: pointer;
      color: var(--text-secondary);
      font-size: 14px;
      border: 1px solid var(--button-text-disabled);
      height: 36px;
    }

    .permissions-panel__added-group-members-count {
      font-size: 12px;
      color: var(--snackbar-action-text);
      margin-top: 5px;
      cursor: pointer;

        &:hover {
          color: var(--button-light-blue);
        }
    }
  }

  .permissions-panel__apply-button {
    background-color: var(--button-dark-blue);

    &:disabled {
      background-color: var(--button-background-disabled);
    }
  }

  .permissions-panel__no-users-or-groups {
    height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-weight: 700;
  }

  .permissions-panel__no-users-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin-top: 10px;
    font-weight: 400;
  }

  .permissions-panel__user-avatar {
    width: 20px;
    height: 20px;
    background-color: var(--background-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .permissions-panel__footer-container {
    gap: 10px;
    padding: 30px;
    display: flex;
    justify-content: flex-end;
    background-color: var(--background-primary);
    }
}