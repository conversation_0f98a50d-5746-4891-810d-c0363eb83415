import './index.scss';
import { noStatusLabelsTagsFound, searchEmptyExplore } from '@assets/images';
import { I18nTranslate } from '@i18n';

interface EmptyStateProps {
  startExploring?: boolean;
  selectACategory?: boolean;
}

const EmptyState = ({ startExploring, selectACategory }: EmptyStateProps) => {
  const intl = I18nTranslate.Intl();
  return (
    <tr>
      <td className="empty-state__container">
        {selectACategory ? (
          <>
            <img
              src={searchEmptyExplore}
              alt={intl.formatMessage({ id: 'emptyState' })}
            />
            <h2>{intl.formatMessage({ id: 'selectACategory' })}</h2>
            <p>{intl.formatMessage({ id: 'clickTheCategoryWheel' })}</p>
          </>
        ) : startExploring ? (
          <>
            <img
              src={searchEmptyExplore}
              alt={intl.formatMessage({ id: 'emptyState' })}
              draggable={false}
            />
            <h2>{intl.formatMessage({ id: 'startExploring' })}</h2>
            <p>{intl.formatMessage({ id: 'searchForAnything' })}</p>
          </>
        ) : (
          <>
            <img
              src={noStatusLabelsTagsFound}
              alt={intl.formatMessage({ id: 'emptyState' })}
              draggable={false}
            />
            <h2>{intl.formatMessage({ id: 'noResultsFound' })}</h2>
            <p>{intl.formatMessage({ id: 'refineAndTryAgain' })}</p>
          </>
        )}
      </td>
    </tr>
  );
};

export default EmptyState;
