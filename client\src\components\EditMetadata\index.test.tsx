import { configureAppStore } from '@store/index';
import { screen, waitFor } from '@testing-library/dom';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { describe, expect, it, vi } from 'vitest';
import EditMetadataDrawer from '.';
import { render } from '../../../test/render';

const initialStateForMock = {
  metadata: {
    openEditDrawer: false,
    metadata: {
      // byTdoId: {},
      byFolderId: {},
    },
    tdoMetadata: {
      // status: 'idle',
      data: {
        aiwareTdoId: 'tdoId123',
        description: 'description123',
        uploadedDate: '05/16/2025',
        fileSize: '2.5 MB',
        duration: '00:02:30',
        fileFormat: 'mp4',
        fileName: 'bloomberg.mp4',
        caseId: 'caseId123',
        caseName: 'caseName123',
        aiCognitionEngineOutput: [],
        sourceName: '',
        creator: 'Test User',
        sourceId: '',
        evidenceType: '',
        contentType: 'video',
        fileStatus: 'processed',
        summary: { text: 'summary123' },
      },
    },
  },
};

const missingDataStateForMock = {
  metadata: {
    openEditDrawer: false,
    metadata: {
      // byTdoId: {},
      byFolderId: {},
    },
    tdoMetadata: {
      // status: 'idle',
      data: {
        aiwareTdoId: 'tdoId123',
        description: 'description123',
        uploadedDate: '05/16/2025',
        fileFormat: 'mp4',
        fileName: 'bloomberg.mp4',
        caseId: 'caseId123',
        caseName: 'caseName123',
        aiCognitionEngineOutput: [],
        sourceName: '',
        creator: 'Test User',
        sourceId: '',
        evidenceType: '',
        contentType: 'video',
        fileStatus: 'processed',
        summary: { text: 'summary123' },
      },
    },
  },
};

describe('edit metadata', () => {
  it('should render correctly', () => {
    // the mockData.uploadDate: '2024-03-26T00:00:00Z' is UTC.
    vi.stubEnv('TZ', 'UTC');
    const store = configureAppStore(initialStateForMock);

    const { asFragment } = render(
      <Provider store={store}>
        <EditMetadataDrawer />
      </Provider>
    );
    expect(asFragment()).toMatchSnapshot();
    expect(screen.getByText('Edit Metadata')).toBeInTheDocument();
    expect(screen.getByText('File Name')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Summary')).toBeInTheDocument();
    expect(screen.getByText('Upload Date')).toBeInTheDocument();
    expect(screen.getByText('File Size')).toBeInTheDocument();
    expect(screen.getByText('File Format')).toBeInTheDocument();
    expect(screen.getByText('Content Type')).toBeInTheDocument();
    expect(screen.getByText('File ID')).toBeInTheDocument();
    expect(screen.getByText('Duration')).toBeInTheDocument();
    expect(screen.getByText('Created By')).toBeInTheDocument();
    expect(screen.getByText('Asset Status')).toBeInTheDocument();
    expect(screen.getByText('Evidence Type')).toBeInTheDocument();
    expect(screen.getByText('Case ID')).toBeInTheDocument();
    // expect(screen.getByText('Source')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /save changes/i })
    ).toBeDisabled();
  });

  it('should render without duration or file size', () => {
    // the mockData.uploadDate: '2024-03-26T00:00:00Z' is UTC
    vi.stubEnv('TZ', 'UTC');
    const store = configureAppStore(missingDataStateForMock);

    render(
      <Provider store={store}>
        <EditMetadataDrawer />
      </Provider>
    );
    expect(screen.getByText('Edit Metadata')).toBeInTheDocument();
    expect(screen.getByText('File Name')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Summary')).toBeInTheDocument();
    expect(screen.getByText('Upload Date')).toBeInTheDocument();
    expect(screen.queryByText('File Size')).toBeNull();
    expect(screen.getByText('File Format')).toBeInTheDocument();
    expect(screen.getByText('Content Type')).toBeInTheDocument();
    expect(screen.getByText('File ID')).toBeInTheDocument();
    expect(screen.queryByText('Duration')).toBeNull();
    expect(screen.getByText('Created By')).toBeInTheDocument();
    expect(screen.getByText('Asset Status')).toBeInTheDocument();
    expect(screen.getByText('Evidence Type')).toBeInTheDocument();
    expect(screen.getByText('Case ID')).toBeInTheDocument();
    // expect(screen.getByText('Source')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /save changes/i })
    ).toBeDisabled();
  });

  it.skip('should handle validate fields', async () => {
    const { container } = render(<EditMetadataDrawer />);

    const fileName = container.querySelector('input[name="fileName"]')!;
    // const description = container.querySelector('input[name="description"]')!;
    // const caseName = container.querySelector('input[name="caseName"]')!;
    // const caseId = container.querySelector('input[name="caseId"]')!;

    const submitBtn = screen.getByRole('button', { name: /save changes/i });

    expect(submitBtn).toBeDisabled();
    userEvent.clear(fileName);
    // userEvent.type(description, 'description');
    // userEvent.clear(caseName);
    // userEvent.clear(caseId);

    userEvent.click(submitBtn);
    await waitFor(() => {
      expect(screen.getByText('File Name is required')).toBeInTheDocument();
      // expect(screen.getByText('Case Name is required')).toBeInTheDocument();
    });

    // userEvent.type(caseId, 'caseId@123');
    await waitFor(() => {
      expect(
        screen.getByText('Only letters, numbers, _ , and - allowed')
      ).toBeInTheDocument();
    });
  });
});
