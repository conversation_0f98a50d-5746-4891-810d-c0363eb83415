import { Request, Response } from 'express';
import { processScheduledEvent } from '../processScheduledEvent';

export async function scheduledEvent(req: Request, res: Response) {
  console.info('scheduledEvent - Received scheduledEvent', req.body);
  if (!req.body?.authorization?.tokens?.[0]) {
    console.error(
      'scheduledEvent - No token found in scheduledEvent:',
      req.body
    );
    // Respond with a 200 otherwise the platform will resending the event.
    res
      .status(200)
      .json({ message: 'No token found in scheduledEvent', body: req.body });
    return;
  }
  const token = req.body.authorization.tokens[0];
  const appConfig = req.app.locals.appConfig;
  const orgExpirationMap = req.app.locals.orgExpirationMap;
  try {
    const result = await processScheduledEvent({
      token,
      appConfig,
      orgExpirationMap,
    });
    res.status(200).json({
      message: 'scheduledEvent processed successfully',
      result,
    });
    return;
  } catch (error) {
    console.error('scheduledEvent - Failed processing scheduledEvent', error);
    // Respond with a 200 otherwise the platform will resending the event.
    res.status(200).json({ message: 'Failed processing scheduledEvent' });
    return;
  }
}
