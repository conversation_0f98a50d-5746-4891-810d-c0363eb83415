declare namespace Cypress {
  interface UserInfo {
    body: {
      token: string;
      userId: string;
    };
  }
  interface Chainable {
    Graphql(
      query: string,
      variables?: unknown
      // TO DO
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ): Chainable<Cypress.Response<any>>;
    request<T = unknown>(
      options: Partial<RequestOptions>
    ): Chainable<Response<T>>;
    LoginToApp(): Chainable;
    LoginAndSwitchOrg(organizationGuid: string, userName: string): Chainable;
    loginAsUser(userKey: string): Chainable;
    LoginLandingPage(): Chainable;
    repeat({
      action,
      times,
    }: {
      action: unknown;
      times: number;
    }): Chainable<void>;
    awaitNetworkResponseCode({
      alias,
      code,
      repeat,
      timeout,
    }: {
      alias: string;
      code: number;
      repeat?: number;
      timeout?: number;
    }): Chainable<void>;
    filterByAria(attr: string, value: string): Chainable<JQuery>;
    getByRoles(role: string): Chainable<JQuery>;
    interceptGraphQLQuery(query: string, alias: string): Chainable<void>;
    getDataIdCy({
      idAlias,
      options,
    }: {
      idAlias: string;
      options?: Partial<
        Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow
      >;
    }): Cypress.Chainable<JQuery<HTMLElement>>;
    SelectFile(options: {
      fileName: string;
      selector: string;
    }): Chainable<void>;
    getState(): Chainable<import('../../src/store').RootState>;
    assertNoLoading(): Chainable;
    deleteStatusByName(statusName: DataTable): Chainable;
    deleteTagByName(tagName: DataTable): Chainable;
    deleteCaseById(caseId: string): Chainable;
    assertTableColumnSorted(label: string, orderBy: string): Chainable;
    dragToChangePosition(
      currentPosition: number,
      targetPosition: number
    ): Chainable;
    scrollFileAndDoubleClickUntilFound(
      containerAlias: string,
      searchText: string,
      maxAttempts?: number
    ): Chainable<JQuery<HTMLElement>>;
    checkItemsByLabelText<K extends string, T extends Record<K, string>>(
      sectionTitlePattern: RegExp,
      items: T[],
      itemKey: K,
      isReset?: boolean
    ): Chainable<void>;
    pressKeyOnTable(key: string): Chainable<void>;
    verifyTableRowSelected(position: string): Chainable<void>;
  }
  interface RequestOptions extends Loggable, Timeoutable, Failable {
    auth: object;
    body: RequestBody;
    encoding: Encodings;
    followRedirect: boolean;
    form: boolean;
    gzip: boolean;
    headers: object;
    method: HttpMethod;
    qs: object;
    url: string;
  }
}
