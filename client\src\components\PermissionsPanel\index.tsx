import './index.scss';
import { Close as CloseIcon, Search as SearchIcon } from '@mui/icons-material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { I18nTranslate } from '@i18n';
import {
  Avatar,
  Box,
  Button,
  IconButton,
  InputAdornment,
  ListItemIcon,
  MenuItem,
  Paper,
  Popper,
  Select,
  Tab,
  Tabs,
  TextField,
} from '@mui/material';
import { MemberType, User } from '@utils/olp/types.ts';
import { useEffect, useRef, useState } from 'react';
import { FixedSizeList, ListChildComponentProps } from 'react-window';
import { groupIcon } from '@assets/images';
import cn from 'classnames';
import Drawer from '@components/Drawer';
import GroupUsersListPanel from '@components/PermissionsPanel/GroupUersListPanel/GroupUsersListPanel.tsx';
import {
  addMembersToFolderAction,
  authPermissionSets,
  getPermissionDataForCase,
  removeMembersFromFolderAction,
  selectCasePermissionData,
  selectEditingCaseFolderId,
  selectInvestigateEditorPermissionSet,
  selectInvestigateViewerPermissionSet,
  updatePermissionsForFolder,
} from '@store/modules/caseManager/slice';
import { useSelector } from 'react-redux';
import { useAppDispatch } from '@store/hooks.ts';
import {
  getNonPermissionedUsersAndGroups,
  getPermissionedUserAndGroups,
} from '@components/PermissionsPanel/permission-utils.ts';

const LIST_ITEM_SIZE = 60;
const LIST_MAX_VISIBLE = 8;

export enum TabType {
  people = 'peopleTab',
  group = 'groupsTab',
}

interface PermissionsPanelProps {
  onClose: () => void;
}

export interface userOrGroupOption {
  id: string;
  memberId: string;
  name: string | undefined;
  firstName?: string;
  lastName?: string;
  memberType: MemberType;
  email?: string;
  imageUrl?: string;
  count?: number;
  permissionSet?: {
    permissionId?: string;
    permissionName?: string;
    permissionDescription?: string;
  };
  members?: User[];
  objectMemberType?: string;
}

const PermissionsPanel = ({ onClose }: PermissionsPanelProps) => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const [userOrGroupOptions, setUserOrGroupOptions] = useState<
    userOrGroupOption[]
  >([]);
  const [addedUsersAndGroups, setAddedUsersAndGroups] = useState<
    userOrGroupOption[]
  >([]);
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<FixedSizeList>(null);
  const listOuterRef = useRef<HTMLDivElement>(null);
  const [open, setOpen] = useState(false);
  const [dropdownWidth, setDropdownWidth] = useState<number>(0);
  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);
  const [selectedTab, setSelectedTab] = useState<TabType>(TabType.people);
  const [groupUsersListPanelOpen, setGroupUsersListPanelOpen] = useState(false);
  const [currentlySelectedUserOrGroup, setCurrentlySelectedUserOrGroup] =
    useState<userOrGroupOption | null>(null);
  const editingCaseFolderId = useSelector(selectEditingCaseFolderId);
  const casePermissionData = useSelector(selectCasePermissionData);
  const viewerPermissionSet = useSelector(selectInvestigateViewerPermissionSet);
  const editorPermissionSet = useSelector(selectInvestigateEditorPermissionSet);

  const allowedPermissionSets = [viewerPermissionSet, editorPermissionSet];

  // Set dropdown width based on container width
  useEffect(() => {
    if (open && containerRef.current) {
      setDropdownWidth(containerRef.current.offsetWidth);
    }
  }, [open]);

  useEffect(() => {
    if (editingCaseFolderId) {
      dispatch(
        getPermissionDataForCase({
          folderId: editingCaseFolderId,
        })
      );
    }
    dispatch(authPermissionSets());
  }, []);

  useEffect(() => {
    const userGroupSelectOptions = getNonPermissionedUsersAndGroups(
      casePermissionData.casePermissionedMembers,
      casePermissionData.allOrgGroupMembers,
      casePermissionData.allOrgUsers
    );
    setUserOrGroupOptions(userGroupSelectOptions);
    const permissionedUsersAndGroups = getPermissionedUserAndGroups(
      casePermissionData.casePermissionedMembers,
      casePermissionData.allOrgGroupMembers,
      casePermissionData.allOrgUsers
    );
    setAddedUsersAndGroups(permissionedUsersAndGroups);
  }, [casePermissionData]);

  const handleAddUserOrGroup = (userOrGroup: userOrGroupOption) => {
    const permissionSetId =
      userOrGroup.permissionSet?.permissionId || viewerPermissionSet?.id;

    if (
      userOrGroup.memberType === MemberType.Group ||
      userOrGroup.memberType === MemberType.User
    ) {
      const membersToAdd = [
        {
          id: userOrGroup.id,
          memberType: userOrGroup.memberType,
        },
      ];

      if (editingCaseFolderId && permissionSetId) {
        console.log(
          'Adding group members to folder:',
          editingCaseFolderId,
          membersToAdd,
          permissionSetId
        );
        dispatch(
          addMembersToFolderAction({
            folderId: editingCaseFolderId,
            members: membersToAdd,
            permissionSetID: permissionSetId,
          })
        );
      }
    }
    setSelectedTab(
      userOrGroup.memberType === MemberType.User
        ? TabType.people
        : TabType.group
    );
    // setAddedUsersAndGroups([...addedUsersAndGroups]);
    setInputValue('');
    setOpen(false);
  };

  const handleChangeTab = (newValue: TabType) => {
    setSelectedTab(newValue);
  };

  const getFirstLastInitials = (user: userOrGroupOption) => {
    const firstNameInitial = user.firstName ? user.firstName[0] : '';
    const lastNameInitial = user.lastName ? user.lastName[0] : '';
    return `${firstNameInitial}${lastNameInitial}`;
  };

  const renderRow = ({ index, style }: ListChildComponentProps) => {
    // TODO: put this in to handle loading??
    // if (index === userOrGroupOptions.length) {
    //   return (
    //     <MenuItem
    //       style={{ ...style, width: dropdownWidth }}
    //       disabled
    //       key="loading-more"
    //     >
    //       {loading === 'loading' ? (
    //         <CircularProgress size={20} />
    //       ) : (
    //         intl.formatMessage({ id: 'loading' })
    //       )}
    //     </MenuItem>
    //   );
    // }
    const getItemIcon = (option: userOrGroupOption) => {
      if (option.memberType === MemberType.Group) {
        return (
          <ListItemIcon sx={{ minWidth: 36 }}>
            <img
              src={groupIcon}
              alt={intl.formatMessage({ id: 'emptyState' })}
            />
          </ListItemIcon>
        );
      } else {
        return (
          <ListItemIcon sx={{ minWidth: 36 }}>
            <div className="permissions-panel__user-avatar">
              {option.imageUrl ? (
                <img src={option.imageUrl} alt={option.name} />
              ) : (
                <Avatar
                  sx={{
                    bgcolor: 'var(--button-text-disabled)',
                    width: 26,
                    height: 26,
                    fontSize: 12,
                  }}
                >
                  {getFirstLastInitials(option)}
                </Avatar>
              )}
            </div>
          </ListItemIcon>
        );
      }
    };

    if (userOrGroupOptions.length > 0) {
      const option = userOrGroupOptions[index];
      return (
        <MenuItem
          style={{ ...style, width: dropdownWidth }}
          key={option.id}
          selected={index === highlightedIndex}
          onClick={() => {
            handleAddUserOrGroup(option);
            setOpen(false);
          }}
          onMouseEnter={() => setHighlightedIndex(index)}
          data-testid={`permission-select-item-${option.id}`}
        >
          <div>{getItemIcon(option)}</div>
          <div>
            <div>{option.name}</div>
            <div>{option.email}</div>
          </div>
        </MenuItem>
      );
    }
  };

  const handleRemoveUserOrGroup = (id: string, memberType: MemberType) => {
    dispatch(
      removeMembersFromFolderAction({
        members: [
          {
            id,
            memberType,
          },
        ],
      })
    );
  };

  const handlePermissionChange = (
    userOrGroup: userOrGroupOption,
    permissionIdSelected: string
  ) => {
    if (editingCaseFolderId) {
      dispatch(
        updatePermissionsForFolder({
          folderId: editingCaseFolderId,
          members: [
            {
              id: userOrGroup.id,
              memberType: userOrGroup.memberType,
            },
          ],
          permissionSetID: permissionIdSelected,
          membersToAdd: [
            {
              id: userOrGroup.memberId, // Note the difference here, using memberId not id
              memberType: userOrGroup.memberType,
            },
          ],
        })
      );
    }
  };

  const renderAddedUsers = () => {
    const filteredAddedUsers = addedUsersAndGroups.filter(
      (userOrGroup) => userOrGroup.memberType === MemberType.User
    );

    if (filteredAddedUsers.length === 0) {
      return (
        <div className={'permissions-panel__no-users-or-groups'}>
          <div>{intl.formatMessage({ id: 'noUsersFound' })}</div>
          <div className="permissions-panel__no-users-subtitle">
            {intl.formatMessage({ id: 'searchForUsersOrGroups' })}
          </div>
        </div>
      );
    }

    return filteredAddedUsers.map((user) => (
      <div
        key={user.id}
        className="permissions-panel__added-user"
        data-testid={`added-user-${user.id}`}
      >
        <div className={'permissions-panel__added-user-group-icon'}>
          <div className="permissions-panel__user-avatar">
            {user.imageUrl ? (
              <img src={user.imageUrl} alt={user.name} />
            ) : (
              <Avatar
                sx={{
                  bgcolor: 'var(--button-text-disabled)',
                  width: 30,
                  height: 30,
                  fontSize: 12,
                }}
              >
                {getFirstLastInitials(user)}
              </Avatar>
            )}
          </div>
        </div>
        <div className={'permissions-panel__added-user-name'}>
          <div>{user.name}</div>
          <div className={'permissions-panel__add-user-email'}>
            {user.email}
          </div>
        </div>
        <div className={'permissions-panel__added-user-select-container'}>
          <Select
            value={user.permissionSet?.permissionId} // Default value, can be changed based on user selection
            className={'permissions-panel__added-user-select'}
            sx={{
              '&.MuiOutlinedInput-notchedOutline': {
                borderColor: 'black', // Change to your desired color
                borderWidth: '1px',
              },
            }}
            onChange={(event) =>
              handlePermissionChange(user, event.target.value)
            }
          >
            {allowedPermissionSets.map((permSet) => (
              <MenuItem
                key={permSet?.id}
                value={permSet?.id}
                sx={{
                  '&:hover': {
                    backgroundColor: 'lightgray', // Your desired hover background color
                  },
                }}
              >
                {permSet?.name.toLowerCase().includes('viewer')
                  ? intl.formatMessage({ id: 'permissionSetViewer' })
                  : intl.formatMessage({ id: 'permissionSetEditor' })}
              </MenuItem>
            ))}
          </Select>
        </div>
        <Button
          className={'permissions-panel__added-user-remove'}
          onClick={() => handleRemoveUserOrGroup(user.id, user.memberType)}
          data-testid={`remove-user-button-${user.email}`}
        >
          {intl.formatMessage({ id: 'remove' })}
        </Button>
      </div>
    ));
  };

  const renderAddedGroups = () => {
    const filteredAddedGroups = addedUsersAndGroups.filter(
      (userOrGroup) => userOrGroup.memberType === MemberType.Group
    );

    if (filteredAddedGroups.length === 0) {
      return (
        <div className={'permissions-panel__no-users-or-groups'}>
          <div>{intl.formatMessage({ id: 'noGroupsFound' })}</div>
          <div className="permissions-panel__no-users-subtitle">
            {intl.formatMessage({ id: 'searchForUsersOrGroups' })}
          </div>
        </div>
      );
    }

    return filteredAddedGroups.map((group) => (
      <div
        key={group.id}
        className="permissions-panel__added-user"
        data-testid={`added-user-${group.id}`}
      >
        <div className={'permissions-panel__added-user-group-icon'}>
          <img src={groupIcon} alt={intl.formatMessage({ id: 'emptyState' })} />
        </div>
        <div className={'permissions-panel__added-user-name'}>
          <div className={'permissions-panel__added-user-name'}>
            <div>{group.name}</div>
            <div
              onClick={() => {
                setCurrentlySelectedUserOrGroup(group);
                setGroupUsersListPanelOpen(true);
              }}
              className={'permissions-panel__added-group-members-count'}
            >
              {intl.formatMessage(
                { id: 'membersCount' },
                { count: `${group.count}` }
              )}
            </div>
          </div>
        </div>
        <div className={'permissions-panel__added-user-select-container'}>
          <Select
            // TODO: How do we handle the value here.   Where is the permission?
            value={group.permissionSet?.permissionId}
            defaultValue={'view'}
            className={'permissions-panel__added-user-select'}
            onChange={(event) =>
              handlePermissionChange(group, event.target.value)
            }
            sx={{
              '&.MuiOutlinedInput-notchedOutline': {
                borderColor: 'black', // Change to your desired color
                borderWidth: '1px',
              },
            }}
          >
            {allowedPermissionSets.map((permSet) => (
              <MenuItem
                key={permSet?.id}
                value={permSet?.id}
                sx={{
                  '&:hover': {
                    backgroundColor: 'lightgray', // Your desired hover background color
                  },
                }}
              >
                {permSet?.name.toLowerCase().includes('viewer')
                  ? intl.formatMessage({ id: 'permissionSetViewer' })
                  : intl.formatMessage({ id: 'permissionSetEditor' })}
              </MenuItem>
            ))}
          </Select>
        </div>
        <Button
          className={'permissions-panel__added-user-remove'}
          onClick={() => handleRemoveUserOrGroup(group.id, group.memberType)}
        >
          {intl.formatMessage({ id: 'remove' })}
        </Button>
      </div>
    ));
  };

  const searchUsersAndGroups = (searchText: string) => {
    if (!searchText) {
      setUserOrGroupOptions([]);
      return;
    }

    const userGroupSelectOptions = getNonPermissionedUsersAndGroups(
      casePermissionData.casePermissionedMembers,
      casePermissionData.allOrgGroupMembers,
      casePermissionData.allOrgUsers
    );

    // Get the users
    const filteredUsers = userGroupSelectOptions.filter(
      (userOrGroup) =>
        userOrGroup.memberType === MemberType.User &&
        `${userOrGroup.firstName} ${userOrGroup.lastName}`
          .toLowerCase()
          .includes(searchText.toLowerCase())
    );

    const filteredGroups = userGroupSelectOptions.filter((userOrGroup) => {
      // Filter groups based on search text
      if (
        userOrGroup.memberType === MemberType.Group &&
        userOrGroup.members &&
        userOrGroup.name
      ) {
        return userOrGroup.name
          .toLowerCase()
          .includes(searchText.toLowerCase());
      }
    });

    const combinedResults: userOrGroupOption[] = [
      ...filteredUsers,
      ...filteredGroups,
    ];

    setUserOrGroupOptions(combinedResults);
  };

  return (
    <div
      className="permissions-panel"
      data-testid="permissions-panel-container"
    >
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <div className="permissions-panel__title-container">
          <div
            className="permissions-panel__heading-title"
            data-testid="permissions-panel-title"
          >
            {I18nTranslate.TranslateMessage('permissions')}
          </div>
          <div>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </div>
        </div>
        <Box role="presentation" className="permissions-panel__main-container">
          <div className={'permissions-panel__subtitle-text'}>
            {I18nTranslate.TranslateMessage(
              'grantAccessForCaseToPeopleOrGroups'
            )}
          </div>
          <div
            ref={containerRef}
            className="permissions-panel__vertical-field-gap"
          >
            <TextField
              fullWidth
              data-testid={'permissions-search-select'}
              placeholder={intl.formatMessage({ id: 'addPeopleOrGroups' })}
              value={inputValue}
              inputRef={inputRef}
              onChange={(e) => {
                if (e.target.value === '') {
                  setOpen(false);
                } else {
                  setOpen(true);
                }
                setInputValue(e.target.value);
                searchUsersAndGroups(e.target.value);
              }}
              slotProps={{
                htmlInput: {
                  'data-testid': 'permission-select-search-input',
                },
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize={'medium'} />
                    </InputAdornment>
                  ),
                  // className: cn('multi-select__search-input', {
                  //   'multi-select__filter-search-input': isFilterCaseTags,
                  // }),
                },
              }}
            />
          </div>
          <Popper
            open={open}
            anchorEl={containerRef.current}
            placement="bottom-start"
            style={{ zIndex: 1300, width: dropdownWidth }}
          >
            <Paper
              elevation={3}
              style={{
                marginTop: 4,
                maxHeight: LIST_ITEM_SIZE * LIST_MAX_VISIBLE,
                width: dropdownWidth,
              }}
            >
              <FixedSizeList
                ref={listRef}
                outerRef={listOuterRef}
                height={
                  Math.min(LIST_MAX_VISIBLE, userOrGroupOptions.length) *
                  LIST_ITEM_SIZE
                }
                width={dropdownWidth}
                itemSize={60}
                itemCount={userOrGroupOptions.length}
              >
                {renderRow}
              </FixedSizeList>
            </Paper>
          </Popper>
          <div className="permissions-panel__vertical-field-gap">
            <Box className="permissions-panel__tabs-container">
              <Tabs
                value={selectedTab}
                orientation="horizontal"
                onChange={(_e, newValue) => {
                  handleChangeTab(newValue as TabType);
                }}
                TabIndicatorProps={{
                  sx: {
                    backgroundColor: 'var(--text-primary)',
                  },
                }}
              >
                <Tab
                  label={I18nTranslate.TranslateMessage('people')}
                  value="peopleTab"
                  className={cn('permissions-panel__tab', {
                    selected: selectedTab === TabType.people,
                  })}
                  sx={{
                    '&.Mui-selected': {
                      boxShadow: '0 0px 0px rgba(0, 0, 0, 0.1)', // no bar on left side
                    },
                  }}
                  data-testid="peoples-tab"
                />
                <Tab
                  label={I18nTranslate.TranslateMessage('groups')}
                  value="groupsTab"
                  className={cn('permissions-panel__tab', {
                    selected: selectedTab === TabType.group,
                  })}
                  sx={{
                    '&.Mui-selected': {
                      boxShadow: '0 0px 0px rgba(0, 0, 0, 0.1)', // no bar on left side
                    },
                  }}
                  data-testid="groups-tab"
                />
              </Tabs>
            </Box>
            {selectedTab === TabType.people ? (
              <div>{renderAddedUsers()}</div>
            ) : (
              <div>{renderAddedGroups()}</div>
            )}
          </div>
        </Box>
        <div className="permissions-panel__footer-container">
          <Button
            variant="contained"
            onClick={onClose}
            className="permissions-panel__apply-button"
            data-testid="permissions-panel-apply-button"
          >
            {I18nTranslate.TranslateMessage('done')}
          </Button>
        </div>
      </LocalizationProvider>
      <Drawer
        open={groupUsersListPanelOpen}
        anchor="right"
        onClose={() => setGroupUsersListPanelOpen(false)}
        width={465}
      >
        <GroupUsersListPanel
          onClose={() => setGroupUsersListPanelOpen(false)}
          currentGroup={currentlySelectedUserOrGroup as userOrGroupOption}
        />
      </Drawer>
    </div>
  );
};

export default PermissionsPanel;
