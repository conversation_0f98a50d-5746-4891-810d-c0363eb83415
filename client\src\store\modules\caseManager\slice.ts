import { Case } from '@components/CaseManager/CaseTable';
import { I18nTranslate } from '@i18n';
import { GetThunkAPI, PayloadAction } from '@reduxjs/toolkit';
import {
  BasicUserInfo,
  CaseFilterValue,
  CaseResult,
  CaseSearchResults,
  InvestigateCase,
  InvestigateCaseSDO,
} from '@shared-types/types';
import { createAppSlice } from '@store/createAppSlice';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/index';
import { fetchStatuses, fetchTags } from '@store/modules/settings/slice';
import { ApiStatus } from '@store/types';
import { getApiAuthToken } from '@store/utils';
import { GQLApi, SortBy } from '@utils/helpers';
import {
  addCaseStatusToLocalStorage,
  CASES_FOLDER_KEY,
  CaseStatus,
  EDIT_CASE_KEY,
  getLocalStorage,
  setLocalStorage,
} from '@utils/local-storage';
import { enqueueSnackbar } from 'notistack';
import { checkCreateRootFolder } from './checkCreateRootFolder';
import { createCase } from './createCase';
import { deleteCaseBySdoId } from './deleteCase';
import { fetchFileName } from './fetchFileName';
import { getCase } from './getCase';
import { getNameOrEmail, getOwner } from '@utils/getOwners';
import { searchCases as searchCasesFn } from './searchCases';
import { softDeleteFile } from './softDeleteFile';
import { updateCase } from './updateCase';
import {
  addMembersToFolder,
  getFolderMembers,
  removeMembersFromFolder,
} from '@utils/olp/folder.ts';
import {
  ACLRecordType,
  AuthGroup,
  Member,
  User,
  AuthPermissionSet,
} from '@utils/olp/types.ts';
import { getGroups } from '@utils/olp/group.ts';
import { getOrgUsers } from '@utils/olp/user.ts';
import { selectEvidenceTypeSchema } from '../config/slice';
import { fetchMetadata } from '../metadata/slice';
import {
  getAuthPermissionSets,
  Investigate_Editor_Permission_Set_Name,
  Investigate_Viewer_Permission_Set_Name,
} from '@utils/olp/permissions';
import axios from 'axios';
import { getValidCaseIds } from './getValidCaseIds';

interface CreateCaseResult {
  folderId: string;
  caseId: string;
  investigateCase: InvestigateCase;
  userId: string;
  sdoId: string;
}

export interface CaseManagerSliceState {
  createCase: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  deleteCase: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  rootFolder: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  folderContentTemplateSchema: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  authPermissionSets: {
    status: ApiStatus;
    error?: string;
    permissons?: {
      investigateEditor: AuthPermissionSet;
      investigateViewer: AuthPermissionSet;
    };
  };
  cases: {
    status: ApiStatus;
    error?: string;
    data: CaseSearchResults['searchMedia']['jsondata'];
  };
  caseValidity: {
    status: 'idle' | 'loading' | 'complete' | 'failure';
    validCases: CaseSearchResults['searchMedia']['jsondata']['results'][0]['folderId'][];
    inValidCases: CaseSearchResults['searchMedia']['jsondata']['results'][0]['folderId'][];
  };
  sortBy: SortBy;
  sortDirection: 'desc' | 'asc';
  selectedCase: {
    status: ApiStatus;
    data: InvestigateCaseSDO;
  };
  editingCaseFolderId?: string;
  deleteFile: {
    status: ApiStatus;
    error?: string;
  };
  allFolderIds: {
    status: ApiStatus;
    error?: string;
    data: {
      id: string;
      name: string;
    }[];
  };
  userInfo: Record<string, BasicUserInfo['basicUserInfo']>;
  isCaseDrawerOpen: boolean;
  isShareDrawerOpen: boolean;
  pollInterval?: NodeJS.Timeout;
  limit: number;
  offset: number;
  caseFilter: CaseFilterValue;
  statusMenuSelectedId: string;
  rowDataTableContext?: CaseResult;
  pendingCaseId: string | null;
  casePermissionData: {
    casePermissionedMembers: Array<ACLRecordType>;
    allOrgGroupMembers: Array<AuthGroup>;
    allOrgUsers: Array<User>;
  };
  getMembersLoading: boolean;
}

export const initialState: CaseManagerSliceState = {
  createCase: {
    status: 'idle',
    error: '',
    id: '',
  },
  deleteCase: {
    status: 'idle',
    error: '',
    id: '',
  },
  rootFolder: {
    status: 'idle',
    error: '',
    id: '',
  },
  folderContentTemplateSchema: {
    status: 'idle',
    error: '',
    id: '',
  },
  authPermissionSets: {
    status: 'idle',
    error: '',
    permissons: undefined,
  },
  cases: {
    status: 'idle',
    error: '',
    data: {
      results: [],
      totalResults: 0,
      limit: 0,
      from: 0,
      to: 0,
    },
  },
  allFolderIds: {
    status: 'idle',
    error: '',
    data: [],
  },
  sortBy: SortBy.CaseDate,
  sortDirection: 'desc',
  selectedCase: {
    status: 'idle',
    data: {
      sdoId: '',
      createdDateTime: '',
      modifiedDateTime: '',
      caseId: '',
      description: '',
      caseDate: '',
      statusId: '',
      preconfiguredTagIds: [],
      folderId: '',
      createdBy: '',
      modifiedBy: '',
    },
  },
  deleteFile: {
    status: 'idle',
    error: '',
  },
  userInfo: {},
  isCaseDrawerOpen: false,
  isShareDrawerOpen: false,
  limit: 50,
  offset: 0,
  editingCaseFolderId: '',
  caseFilter: {},
  caseValidity: {
    status: 'idle',
    validCases: [],
    inValidCases: [],
  },
  statusMenuSelectedId: '',
  rowDataTableContext: {
    caseId: '',
    statusId: '',
    caseDate: '',
    caseName: '',
    folderId: '',
    createdBy: '',
    description: '',
    createdDateTime: '',
    modifiedDateTime: '',
    preconfiguredTagIds: [],
    id: '',
    sdoId: '',
  },
  pendingCaseId: null,
  casePermissionData: {
    casePermissionedMembers: [],
    allOrgGroupMembers: [],
    allOrgUsers: [],
  },
  getMembersLoading: false,
};

export const caseManagerSlice = createAppSlice({
  name: 'caseManager',
  initialState,
  reducers: (create) => {
    const createThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>();
    return {
      setSort: create.reducer(
        (
          state,
          action: PayloadAction<{
            sortBy: SortBy;
            sortDirection: 'asc' | 'desc';
          }>
        ) => {
          state.sortBy = action.payload.sortBy;
          state.sortDirection = action.payload.sortDirection;
        }
      ),
      setLimit: create.reducer((state, action: PayloadAction<number>) => {
        state.limit = action.payload;
      }),
      setOffset: create.reducer((state, action: PayloadAction<number>) => {
        state.offset = action.payload;
      }),
      setCaseFilter: create.reducer(
        (state, action: PayloadAction<CaseFilterValue>) => {
          state.caseFilter = action.payload;
        }
      ),
      openInTrack: createThunk(
        async (
          params: {
            tdoId: string;
            folderId: string;
            folderName: string;
            caseDate: string;
            description: string;
          },
          thunkAPI
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          const getJobStatus = (status: string) => {
            switch (status) {
              case 'no status':
                return 'unknown';
              case 'running':
                return 'processing';
              case 'complete':
                return 'processed';
              case 'failed':
              case 'cancelled':
              case 'aborted':
                return 'error';
              case 'pending':
              case 'queued':
              case 'accepted':
              case 'standby_pending':
              case 'waiting':
              case 'resuming':
              case 'paused':
              default:
                return 'pending';
            }
          };

          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const jobs = await gql.getTdoEngineStatuses({ tdoId: params.tdoId });

          const trackJob = jobs.records.find((j) =>
            j.tasks.records.some((t) => t.engine.name.match(/track/gi))
          );

          if (!trackJob) {
            enqueueSnackbar(
              I18nTranslate.TranslateMessage(
                'trackProcessingHasNotBeenExecuted'
              ),
              {
                variant: 'warning',
              }
            );
            return;
          }

          const trackJobStatus = getJobStatus(trackJob.status);

          if (trackJobStatus === 'processing' || trackJobStatus === 'pending') {
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('trackEngineIsProcessing'),
              {
                variant: 'warning',
              }
            );
            return;
          }

          if (trackJobStatus === 'error') {
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('trackEngineJobFailed'),
              {
                variant: 'error',
              }
            );
            return;
          }

          if (trackJobStatus === 'processed') {
            const resp = await axios.post(
              `${config.trackApiUrl as string}/event/import`,
              {
                folderId: params.folderId,
                name: params.folderName,
                eventStartDate: params.caseDate,
                eventEndDate: params.caseDate,
                description: params.description,
                tdoId: params.tdoId,
              },
              {
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (resp.status === 200 || resp.status === 204) {
              console.log(
                'open',
                `${config.trackUrl as string}/event/${params.folderId}/file/${params.tdoId}`
              );

              window.open(
                `${config.trackUrl as string}/event/${params.folderId}/file/${params.tdoId}`,
                '_blank'
              );
              return;
            }

            if (resp.status === 401) {
              enqueueSnackbar(
                I18nTranslate.TranslateMessage('trackAppNotAvailableForUser'),
                {
                  variant: 'error',
                }
              );
              return;
            }

            if (resp.status !== 200 && resp.status !== 204) {
              enqueueSnackbar(
                I18nTranslate.TranslateMessage(
                  'somethingWentWrongWhileOpeningInTrack'
                ),
                {
                  variant: 'error',
                }
              );
              return;
            }
          }
        }
      ),
      createNewCase: createThunk(
        async (
          investigateCase: InvestigateCase,
          thunkAPI
        ): Promise<CreateCaseResult> => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const rootFolderId = selectRootFolderId(state);
          if (!rootFolderId) {
            throw new Error('no root folder found');
          }
          const folderContentTemplateSchemaId =
            selectFolderContentTemplateSchema(state).id;
          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }
          const { userId, kvp } = state.user.user;
          const investigateEditorPermissionSet =
            selectInvestigateEditorPermissionSet(state);
          if (!investigateEditorPermissionSet) {
            throw new Error('no investigate editor permission set found');
          }
          const { folderId, sdoId } = await createCase({
            investigateCase,
            rootFolderId,
            folderContentTemplateSchemaId,
            userId,
            userName: `${kvp?.firstName ?? ''} ${kvp?.lastName ?? ''}`,
            permissionSetId: investigateEditorPermissionSet.id,
            gql,
          });

          if (!folderId) {
            throw new Error('Case creation failed.');
          }

          // Add new created folderId to local storage
          const lsFolderIds = getLocalStorage<string[]>(CASES_FOLDER_KEY) || [];
          setLocalStorage(CASES_FOLDER_KEY, [...lsFolderIds, folderId]);
          return {
            folderId,
            caseId: investigateCase.caseId,
            investigateCase,
            userId,
            sdoId,
          };
        },
        {
          pending: (state) => {
            state.createCase.status = 'loading';
            state.cases.status = 'loading';
            state.createCase.id = '';
          },
          fulfilled: (state, action) => {
            // TODO: handle create case success
            state.createCase.status = 'complete';
            state.cases.status = 'complete';
            state.createCase.id = action.payload.folderId;

            const newCaseResult: CaseResult = {
              id: action.payload.folderId,
              folderId: action.payload.folderId,
              caseId: action.payload.caseId,
              caseName: action.payload.caseId,
              createdDateTime: new Date().toISOString(),
              modifiedDateTime: new Date().toISOString(),
              description: action.payload.investigateCase.description || '',
              statusId: action.payload.investigateCase.statusId || '',
              preconfiguredTagIds:
                action.payload.investigateCase.preconfiguredTagIds || [],
              createdBy: action.payload.userId,
              caseDate:
                action.payload.investigateCase.caseDate ||
                new Date().toISOString(),
              sdoId: action.payload.sdoId,
            };

            state.cases.data.results.unshift(newCaseResult);
            state.cases.data.totalResults++;

            state.allFolderIds.data.unshift({
              id: action.payload.folderId,
              name: action.payload.caseId,
            });

            state.pendingCaseId = action.payload.folderId;

            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseCreatedSuccessfully'),
              {
                variant: 'success',
              }
            );
          },
          rejected: (state, action) => {
            // TODO: handle create case failure
            state.createCase.status = 'failure';
            state.createCase.id = '';
            state.cases.status = 'failure';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseCreationFailed'),
              {
                variant: 'error',
              }
            );
            console.error('failed to create case', action.error);
          },
        }
      ),
      deleteCase: createThunk(
        async (sdoId: string, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const folderContentTemplateSchemaId =
            selectFolderContentTemplateSchema(state).id;
          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }
          await deleteCaseBySdoId({
            folderContentTemplateSchemaId,
            sdoId,
            userId: state.user.user.userId,
            gql,
          });
          return sdoId;
        },
        {
          pending: (state) => {
            state.deleteCase.status = 'loading';
            state.deleteCase.id = '';
          },
          fulfilled: (state, action) => {
            state.deleteCase.status = 'complete';
            state.deleteCase.id = action.payload;
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseDeletedSuccessfully'),
              {
                variant: 'success',
              }
            );
          },

          rejected: (state, _action) => {
            // TODO: handle create case failure
            state.deleteCase.status = 'failure';
            state.deleteCase.id = '';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseDeletionFailed'),
              {
                variant: 'error',
              }
            );
          },
        }
      ),
      rootFolder: createThunk(
        async (_, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const folderId = await checkCreateRootFolder(gql);
          return folderId;
        },
        {
          pending: (state) => {
            state.rootFolder.status = 'loading';
            state.rootFolder.id = '';
          },
          fulfilled: (state, action) => {
            state.rootFolder.status = 'complete';
            state.rootFolder.id = action.payload;
          },
          rejected: (state, action) => {
            // TODO: add snack bar for failure
            state.rootFolder.status = 'failure';
            state.rootFolder.id = '';
            console.error('failed to create case', action.error);
          },
        }
      ),
      folderContentTemplateSchema: createThunk(
        async (_, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const caseRegistryName = config.dataRegistry.caseRegistry.name;
          const id = await gql.getSDOSchemaIdByName(caseRegistryName);
          return id;
        },
        {
          pending: (state) => {
            state.folderContentTemplateSchema.status = 'loading';
            state.folderContentTemplateSchema.id = '';
          },
          fulfilled: (state, action) => {
            state.folderContentTemplateSchema.status = 'complete';
            state.folderContentTemplateSchema.id = action.payload;
          },
          rejected: (state, action) => {
            state.folderContentTemplateSchema.status = 'failure';
            state.folderContentTemplateSchema.id = '';
            console.error('failed to get case schemaId', action.error);
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('failedGetCaseSchemaId'),
              { variant: 'error' }
            );
          },
        }
      ),
      authPermissionSets: createThunk(
        async (_, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const permissios = await getAuthPermissionSets({ gql });
          const investigateEditor = permissios.find(
            (perm) => perm.name === Investigate_Editor_Permission_Set_Name
          );
          if (!investigateEditor) {
            throw new Error('Investigate Editor permission set not found');
          }
          const investigateViewer = permissios.find(
            (perm) => perm.name === Investigate_Viewer_Permission_Set_Name
          );
          if (!investigateViewer) {
            throw new Error('Investigate Viewer permission set not found');
          }
          return {
            investigateEditor,
            investigateViewer,
          };
        },
        {
          pending: (state) => {
            state.authPermissionSets.status = 'loading';
            state.authPermissionSets.permissons = undefined;
          },
          fulfilled: (state, action) => {
            state.authPermissionSets.status = 'complete';
            state.authPermissionSets.permissons = action.payload;
          },
          rejected: (state, action) => {
            state.authPermissionSets.status = 'failure';
            state.authPermissionSets.permissons = undefined;
            console.error(
              'failed to get Investigate permission set, please set up Investigate Editor and Investigate Viewer permission set',
              action.error
            );
            enqueueSnackbar(
              I18nTranslate.TranslateMessage(
                'failedGetInvestigatePermissionSet'
              ),
              { variant: 'error' }
            );
          },
        }
      ),
      searchCases: createThunk(
        async (
          payload: {
            filteredStatusId?: string[];
            isPolling?: boolean;
          },
          thunkAPI: GetThunkAPI<{
            getState: () => RootState;
            extra: { http: HttpClient };
          }>
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const { filteredStatusId } = payload;

          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const folderContentTemplateSchemaId =
            state.caseManager.folderContentTemplateSchema.id;

          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }

          const { offset, limit, sortBy, sortDirection } = state.caseManager;
          const {
            caseId: caseIdSearchText,
            tagIds,
            statusId,
          } = state.caseManager.caseFilter;

          return await searchCasesFn({
            caseIdSearchText,
            tagIds,
            statusIds: statusId ? [statusId] : undefined,
            limit,
            offset,
            sortBy,
            sortDirection,
            folderContentTemplateSchemaId,
            gql,
            caseStatuses: filteredStatusId ? filteredStatusId : undefined,
          });
        },
        {
          pending: (state, action) => {
            const isPolling = action.meta.arg.isPolling;
            if (!isPolling) {
              state.cases.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            const results = action.payload.results;

            if (state.pendingCaseId) {
              const serverHasOptimisticCase = results.some(
                (c) => c.folderId === state.pendingCaseId
              );

              if (serverHasOptimisticCase) {
                state.pendingCaseId = null;
              } else {
                const optimisticCase = state.cases.data.results.find(
                  (c) => c.folderId === state.pendingCaseId
                );
                if (optimisticCase) {
                  results.unshift(optimisticCase);
                }
              }
            }

            const editedCases = getLocalStorage<Case[]>(EDIT_CASE_KEY) || [];
            const updatedResults = results.map((result) => {
              const editedCase = editedCases.find(
                (edited) => edited.folderId === result.folderId
              );
              return editedCase ? { ...result, ...editedCase } : result;
            });
            const updatedPayload = {
              ...action.payload,
              results: updatedResults,
            };

            state.cases.data = updatedPayload;
            state.cases.status = 'complete';

            // Folder IDs from API
            const folderResultIds = action.payload.results.map(
              (caseResult) => caseResult.folderId
            );
            // Folder IDs from local storage
            const folderLSIds =
              getLocalStorage<string[]>(CASES_FOLDER_KEY) || [];
            // Update local storage, remove folders that are available in the results of API
            const updatedFolderIds = folderLSIds.filter(
              (id) => !folderResultIds.includes(id)
            );
            setLocalStorage(CASES_FOLDER_KEY, updatedFolderIds);
          },
          rejected: (state, action) => {
            state.cases.status = 'failure';
            console.error('failed to search cases', action.error);
          },
        }
      ),
      getAllFolderIds: createThunk(
        async (
          _,
          thunkAPI: GetThunkAPI<{
            getState: () => RootState;
            extra: { http: HttpClient };
          }>
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;

          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const folderContentTemplateSchemaId =
            state.caseManager.folderContentTemplateSchema.id;

          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }

          const validCases = await getValidCaseIds({
            limit: 100,
            offset: 0,
            gql,
          });

          const searchCases = await searchCasesFn({
            limit: 10000,
            offset: 0,
            folderContentTemplateSchemaId,
            gql,
          });
          return { validCases, searchCases };
        },
        {
          pending: (state) => {
            state.allFolderIds.status = 'loading';
          },
          fulfilled: (state, action) => {
            // Valid Folder IDs from API
            const validFolderIds = action.payload.validCases;

            // Folder IDs from API
            const searchFolderIds = action.payload.searchCases.results.map(
              (caseResult) => ({
                id: caseResult.folderId,
                name: caseResult.caseId,
              })
            );

            const folderResultIds = searchFolderIds.filter((folder) =>
              validFolderIds.includes(folder.id)
            );

            // Folder IDs from local storage
            const folderLSIds =
              getLocalStorage<string[]>(CASES_FOLDER_KEY) || [];

            // Update local storage, remove folders that are available in the results of API
            const updatedFolderIds = folderLSIds.filter(
              (id) => !folderResultIds.map((folder) => folder.id).includes(id)
            );
            setLocalStorage(CASES_FOLDER_KEY, updatedFolderIds);

            const allAvailableFolderIds = folderResultIds.filter(
              (folder) => !updatedFolderIds.includes(folder.id)
            );

            state.allFolderIds.data = allAvailableFolderIds;
            state.allFolderIds.status = 'complete';
          },
          rejected: (state, action) => {
            state.allFolderIds.status = 'failure';
            console.error('failed to search cases', action.error);
          },
        }
      ),
      pollCases: createThunk((_, thunkAPI) => {
        const { signal, dispatch, getState } = thunkAPI;
        const state = getState() as RootState;
        const pollingMillis = state.config.casePollingInterval ?? 12000;

        let dispatchPromise: DispatchPromise;

        const pollInterval = setInterval(() => {
          dispatchPromise = dispatch(searchCases({ isPolling: true }));
        }, pollingMillis);

        signal.addEventListener('abort', () => {
          clearInterval(pollInterval);
          dispatchPromise?.abort();
        });
      }),
      pollStatusesTags: createThunk((_, thunkAPI) => {
        const { signal, dispatch, getState } = thunkAPI;
        const state = getState() as RootState;
        const pollingMillis = state.config.caseStatusTagsPollInterval ?? 12000;
        let dispatchPromiseStatuses: DispatchPromise;
        let dispatchPromiseTags: DispatchPromise;

        const pollInterval = setInterval(() => {
          dispatchPromiseStatuses = dispatch(
            fetchStatuses({ isPolling: true })
          );
          dispatchPromiseTags = dispatch(fetchTags({ isPolling: true }));
        }, pollingMillis);

        signal.addEventListener('abort', () => {
          clearInterval(pollInterval);
          dispatchPromiseStatuses?.abort();
          dispatchPromiseTags?.abort();
        });
      }),
      getCaseSDO: createThunk(
        async (payload: { folderId: string; isEdit?: boolean }, thunkAPI) => {
          const { folderId } = payload;
          const { getState, dispatch } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const userInfo = state.caseManager.userInfo;

          const caseRegistryName = config.dataRegistry.caseRegistry.name;
          const caseDetail = await getCase({ folderId, caseRegistryName }, gql);
          const { createdBy, createdByName } = caseDetail;
          if (!createdBy) {
            return { ...caseDetail, createdByName: 'N/A' };
          } else if (userInfo && createdBy) {
            const user = userInfo[createdBy];
            if (user) {
              return { ...caseDetail, createdByName: getNameOrEmail(user) };
            }
          }
          const caseOwner = await getOwner({
            data: {
              createdBy,
              createdByName,
            },
            gql,
            dispatch,
            userInfo,
          });

          return { ...caseDetail, createdByName: caseOwner };
        },
        {
          pending: (state, action) => {
            const isEdit = action.meta.arg.isEdit;
            if (!isEdit) {
              state.selectedCase.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            state.selectedCase.data = action.payload;
            state.selectedCase.status = 'complete';
          },
          rejected: (state, action) => {
            state.selectedCase.status = 'failure';
            console.error('failed to load case for editing', action.error);
          },
        }
      ),
      saveCase: createThunk(
        async (
          caseSdo: Omit<InvestigateCaseSDO, 'modifiedDateTime'>,
          thunkAPI
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }

          const folderContentTemplateSchemaId =
            selectFolderContentTemplateSchema(state).id;
          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const updateInvestigateCase = {
            ...caseSdo,
            modifiedDateTime: new Date().toISOString(),
            modifiedBy: state.user.user.userId,
          };

          const { data } = await updateCase(
            caseSdo.sdoId,
            folderContentTemplateSchemaId,
            updateInvestigateCase,
            gql
          );
          return data;
        },
        {
          pending: (state) => {
            state.selectedCase.status = 'loading';
            console.log('case updating');
          },
          fulfilled: (state, action) => {
            state.selectedCase.status = 'complete';
            state.selectedCase.data = {
              ...state.selectedCase.data,
              ...action.payload,
            };
            state.cases.data.results = state.cases.data.results.map(
              (caseItem) =>
                caseItem.folderId === action.payload.folderId
                  ? { ...caseItem, ...action.payload }
                  : caseItem
            );
            const now = Date.now();
            const newStatus: CaseStatus = {
              id: state.selectedCase.data.sdoId,
              statusId: action.payload.statusId,
              expiresAt: now + 7500,
            };

            addCaseStatusToLocalStorage(newStatus);
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseUpdatedSuccess'),
              {
                variant: 'success',
              }
            );
            const lsEditedCases = getLocalStorage<Case[]>(EDIT_CASE_KEY) || [];
            const updatedEditedCases = lsEditedCases.filter(
              (editedCase) => editedCase.folderId !== action.payload.folderId
            );
            setLocalStorage(EDIT_CASE_KEY, [
              ...updatedEditedCases,
              action.payload,
            ]);
          },
          rejected: (state) => {
            state.selectedCase.status = 'failure';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseUpdateFailed'),
              {
                variant: 'error',
              }
            );
            console.error('failed to update case');
          },
        }
      ),
      deleteFile: createThunk(
        async (
          { tdoId, folderId }: { tdoId: string; folderId?: string },
          thunkAPI
        ) => {
          const { getState, dispatch } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          await softDeleteFile({ tdoId, gql });
          if (folderId) {
            const evidenceTypeSchemaId = selectEvidenceTypeSchema(state).id;

            if (evidenceTypeSchemaId) {
              dispatch(fetchMetadata({ folderId, evidenceTypeSchemaId }));
            }
          }
        },
        {
          pending: (state) => {
            state.deleteFile.status = 'loading';
          },
          fulfilled: (state) => {
            state.deleteFile.status = 'complete';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('fileDeletedSuccessfully'),
              {
                variant: 'success',
              }
            );
          },
          rejected: (state) => {
            state.deleteFile.status = 'failure';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('fileDeletionFailed'),
              {
                variant: 'error',
              }
            );
          },
        }
      ),
      toggleCaseDrawer: create.reducer(
        (state, action: PayloadAction<string | undefined>) => {
          // If payload is undefined, create new case, otherwise edit case
          state.isCaseDrawerOpen = !state.isCaseDrawerOpen;
          state.editingCaseFolderId = action.payload;
          state.selectedCase.status = 'idle';
        }
      ),
      toggleShareDrawer: create.reducer((state) => {
        state.isShareDrawerOpen = !state.isShareDrawerOpen;
      }),
      setUserInfo: create.reducer(
        (state, action: PayloadAction<BasicUserInfo['basicUserInfo']>) => {
          state.userInfo[action.payload.id] = action.payload;
        }
      ),
      fileUploadedSuccessfully: createThunk(
        async ({ tdoId }: { tdoId: string }, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          return await fetchFileName(tdoId, gql);
        },
        {
          fulfilled: (_state, action) => {
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('uploadFileSuccess', {
                name: action.payload,
              }),
              {
                variant: 'success',
              }
            );
          },
          rejected: (_state, action) => {
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('uploadFileSuccess', {
                name: action.meta.arg.tdoId,
              }),
              {
                variant: 'error',
              }
            );
          },
        }
      ),
      setStatusMenuSelectedId: create.reducer(
        (state, action: PayloadAction<string>) => {
          state.statusMenuSelectedId = action.payload;
        }
      ),
      setRowTableContextMenu: create.reducer(
        (state, action: PayloadAction<CaseResult>) => {
          state.rowDataTableContext = action.payload;
        }
      ),
      getPermissionDataForCase: createThunk(
        async ({ folderId }: { folderId: string }, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const permissionedMembers = await getFolderMembers({ gql, folderId });
          const allOrgGroupMembers = await getGroups({
            gql,
          });
          const allOrgUsers = await getOrgUsers({
            gql,
          });

          return {
            data: {
              casePermissionData: permissionedMembers.records,
              allOrgGroupMembers: allOrgGroupMembers.records,
              allOrgUsers: allOrgUsers.records,
            },
          };
        },
        {
          fulfilled: (state, action) => {
            state.casePermissionData.casePermissionedMembers =
              action.payload.data.casePermissionData;
            state.casePermissionData.allOrgGroupMembers =
              action.payload.data.allOrgGroupMembers;
            state.casePermissionData.allOrgUsers =
              action.payload.data.allOrgUsers;
            state.getMembersLoading = false;
          },
          pending: (state) => {
            state.getMembersLoading = true;
          },
          rejected: (state, action) => {
            state.getMembersLoading = false;
            console.error('failed to get case members', action.error);
          },
        }
      ),
      addMembersToFolderAction: createThunk(
        async (
          {
            folderId,
            members,
            permissionSetID,
          }: { folderId: string; members: Member[]; permissionSetID: string },
          thunkAPI
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const permissionedMembers = await addMembersToFolder({
            gql,
            folderId,
            members,
            permissionSetID,
          });

          return {
            data: {
              casePermissionData: permissionedMembers.records,
            },
          };
        },
        {
          fulfilled: (state, action) => {
            state.casePermissionData.casePermissionedMembers =
              action.payload.data.casePermissionData;
            state.getMembersLoading = false;
          },
          pending: (state) => {
            state.getMembersLoading = true;
          },
          rejected: (state, action) => {
            state.getMembersLoading = false;
            console.error('failed to get case members', action.error);
          },
        }
      ),
      updatePermissionsForFolder: createThunk(
        async (
          {
            folderId,
            members,
            permissionSetID,
            membersToAdd,
          }: {
            folderId: string;
            members: Member[];
            permissionSetID: string;
            membersToAdd: Member[]; // Members to add back after removing
          },
          thunkAPI
        ) => {
          // The back-end has no way to update permissions for a member, so we need
          // to remove the member from the folder and then add them back with
          // the same permissions.
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          await removeMembersFromFolder({
            gql,
            members,
          });

          const permissionedMembers = await addMembersToFolder({
            gql,
            folderId,
            members: membersToAdd,
            permissionSetID,
          });

          return {
            data: {
              casePermissionData: permissionedMembers.records,
            },
          };
        },
        {
          fulfilled: (state, action) => {
            state.casePermissionData.casePermissionedMembers =
              action.payload.data.casePermissionData;
            state.getMembersLoading = false;
          },
          pending: (state) => {
            state.getMembersLoading = true;
          },
          rejected: (state, action) => {
            state.getMembersLoading = false;
            console.error('failed to get case members', action.error);
          },
        }
      ),
      removeMembersFromFolderAction: createThunk(
        async ({ members }: { members: Member[] }, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const permissionedMembers = await removeMembersFromFolder({
            gql,
            members,
          });

          return {
            data: {
              casePermissionData: permissionedMembers.records,
            },
          };
        },
        {
          fulfilled: (state, action) => {
            state.casePermissionData.casePermissionedMembers =
              action.payload.data.casePermissionData;
            state.getMembersLoading = false;
          },
          pending: (state) => {
            state.getMembersLoading = true;
          },
          rejected: (state, action) => {
            state.getMembersLoading = false;
            console.error('failed to remove case members', action.error);
          },
        }
      ),
    };
  },
  selectors: {
    selectRootFolderId: (state) => state.rootFolder.id,
    selectFolderContentTemplateSchema: (state) =>
      state.folderContentTemplateSchema,
    selectInvestigateEditorPermissionSet: (state) =>
      state.authPermissionSets.permissons?.investigateEditor,
    selectInvestigateViewerPermissionSet: (state) =>
      state.authPermissionSets.permissons?.investigateViewer,
    selectIsCaseDrawerOpen: (app) => app.isCaseDrawerOpen,
    selectCaseData: (state) => state.selectedCase.data,
    selectCaseStatus: (state) => state.selectedCase.status,
    selectCases: (state) => state.cases,
    selectSortBy: (state) => state.sortBy,
    selectSortDirection: (state) => state.sortDirection,
    selectEditingCaseFolderId: (state) => state.editingCaseFolderId,
    selectLimit: (state) => state.limit,
    selectOffset: (state) => state.offset,
    selectCreateCase: (state) => state.createCase,
    selectCreateCaseStatus: (state) => state.createCase.status,
    selectCaseFilter: (state) => state.caseFilter,
    selectValidCases: (state) => state.caseValidity.validCases,
    selectInvalidCases: (state) => state.caseValidity.inValidCases,
    selectCaseValidityStatus: (state) => state.caseValidity.status,
    selectStatusMenuSelectedId: (state) => state.statusMenuSelectedId,
    selectRowTableContext: (state) => state.rowDataTableContext,
    selectAllFolderIds: (state) => state.allFolderIds.data,
    selectCasePermissionData: (state) => state.casePermissionData,
  },
});

export const {
  searchCases,
  pollCases,
  pollStatusesTags,
  createNewCase,
  deleteCase,
  rootFolder,
  folderContentTemplateSchema,
  authPermissionSets,
  toggleCaseDrawer,
  getCaseSDO,
  saveCase,
  deleteFile,
  setLimit,
  setOffset,
  setSort,
  setCaseFilter,
  setUserInfo,
  fileUploadedSuccessfully,
  toggleShareDrawer,
  setStatusMenuSelectedId,
  setRowTableContextMenu,
  getAllFolderIds,
  openInTrack,
  getPermissionDataForCase,
  addMembersToFolderAction,
  removeMembersFromFolderAction,
  updatePermissionsForFolder,
} = caseManagerSlice.actions;

export const {
  selectRootFolderId,
  selectFolderContentTemplateSchema,
  selectInvestigateEditorPermissionSet,
  selectInvestigateViewerPermissionSet,
  selectIsCaseDrawerOpen,
  selectCaseData,
  selectEditingCaseFolderId,
  selectCaseStatus,
  selectCases,
  selectLimit,
  selectOffset,
  selectCreateCase,
  selectCreateCaseStatus,
  selectSortBy,
  selectSortDirection,
  selectCaseFilter,
  selectValidCases,
  selectInvalidCases,
  selectCaseValidityStatus,
  selectStatusMenuSelectedId,
  selectRowTableContext,
  selectAllFolderIds,
  selectCasePermissionData,
} = caseManagerSlice.selectors;

export const { actions: caseManagerActions, reducer: caseManagerReducer } =
  caseManagerSlice;
