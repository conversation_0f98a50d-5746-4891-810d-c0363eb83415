import './index.scss';
import { AddToCase, EditAttributes } from '@assets/icons';
import Menu from '@components/Menu';
import { I18nTranslate } from '@i18n';
import {
  DeleteOutlined as DeleteOutlinedIcon,
  DescriptionOutlined as DescriptionOutlinedIcon,
  DriveFileMoveOutlined as DriveFileMoveOutlinedIcon,
  ExitToAppOutlined as ExitToAppOutlinedIcon,
  FolderOpenOutlined as FolderOpenOutlinedIcon,
  ImageOutlined as ImageOutlinedIcon,
  MoreHoriz as MoreHorizIcon,
  VideocamOutlined as VideocamOutlinedIcon,
  VisibilityOutlined as VisibilityOutlinedIcon,
  VolumeDownOutlined as VolumeDownOutlinedIcon,
} from '@mui/icons-material';
import {
  Box,
  Checkbox,
  Divider,
  IconButton,
  MenuItem,
  PopoverPosition,
  Tooltip,
} from '@mui/material';
import cn from 'classnames';
import { Duration } from 'luxon';
import { ChangeEvent, useState } from 'react';

const renderFileType = (mimeType: string | undefined) => {
  if (!mimeType) {
    return <DescriptionOutlinedIcon />;
  }
  const fileType = mimeType.match(/^([^\/]+)\//)?.[1];
  switch (fileType) {
    case 'video':
      return <VideocamOutlinedIcon />;
    case 'image':
      return <ImageOutlinedIcon />;
    case 'audio':
      return <VolumeDownOutlinedIcon />;
    default:
      return <DescriptionOutlinedIcon />;
  }
};

const FileCard = ({
  fileId,
  fileName,
  fileDuration,
  caseId,
  dateUploaded,
  thumbnailUrl,
  fileType,
  blurred,
  isChecked,
  onCheck,
  onMove,
  onOpenEditMetadataDrawer,
  onViewFile,
  onViewCase,
  onSendToRedact,
  onSendToTrack,
  onDelete,
  isDefaultThumbnail,
  index,
  isPending,
}: FileCardProps) => {
  const intl = I18nTranslate.Intl();
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [menuPosition, setMenuPosition] = useState<PopoverPosition | undefined>(
    undefined
  );

  const handleOpenEditDrawer = () => {
    setMenuAnchorEl(null);
    setMenuPosition(undefined);
    onOpenEditMetadataDrawer(fileId);
  };

  const withMenuClose = (callback: () => void) => () => {
    setMenuAnchorEl(null);
    setMenuPosition(undefined);
    callback();
  };

  return (
    <div className={cn({ 'disable-file-card': isPending })}>
      <Box
        tabIndex={0}
        className="file-card"
        data-testid="file-card"
        data-index={index}
        onDoubleClick={onViewFile}
        onContextMenu={(e) => {
          e.preventDefault();
          setMenuAnchorEl(e.currentTarget);
          setMenuPosition({ top: e.clientY, left: e.clientX });
        }}
      >
        <Box className="file-card__image" data-testid="file-card-image">
          <img
            alt=""
            style={
              isDefaultThumbnail
                ? { height: '100%' }
                : { width: 40, height: 40 }
            }
            src={thumbnailUrl}
            className={cn({ blurred })}
            data-testid="file-card-image-img"
            draggable={false}
          />
          <Checkbox
            checked={isChecked}
            className="file-card__image__checkbox"
            onChange={(event) => {
              event.stopPropagation();
              onCheck(fileId, event.target.checked, event);
            }}
          />
        </Box>
        <Box className="file-card__strip" alignItems="center">
          {renderFileType(fileType)}
          <Box className="file-card__strip__duration">
            {fileDuration > -1
              ? Duration.fromMillis(fileDuration).toFormat('hh:mm:ss.SS')
              : ''}
          </Box>
        </Box>
        <Box className="file-card__info" flex={1}>
          <Tooltip title={fileName}>
            <Box className="file-card__info__name" onClick={onViewFile}>
              {fileName}
            </Box>
          </Tooltip>
          <Box className="file-card__info__caseId">
            <Box className="info-title">
              {intl.formatMessage({ id: 'caseIdWithColons' })}
            </Box>
            <Tooltip
              title={caseId || intl.formatMessage({ id: 'defaultEmpty' })}
            >
              <Box className="file-card__info__caseId__title">
                {caseId || intl.formatMessage({ id: 'defaultEmpty' })}
              </Box>
            </Tooltip>
          </Box>
          <Box className="file-card__info__uploaded">
            <Box className="info-title">
              {intl.formatMessage({ id: 'uploadedWithColons' })}
            </Box>
            <Box className="file-card__info__uploaded__date">
              {dateUploaded}
            </Box>
          </Box>
        </Box>
        <Box className="file-card__menu">
          <IconButton
            onClick={(e) => {
              setMenuAnchorEl(e.currentTarget);
              setMenuPosition({ top: e.clientY, left: e.clientX });
            }}
            data-testid="file-card-menu-icon"
          >
            <MoreHorizIcon />
          </IconButton>
        </Box>
      </Box>
      <Menu
        size="small"
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        anchorReference={menuPosition ? 'anchorPosition' : 'anchorEl'}
        anchorPosition={menuPosition}
        onClose={() => setMenuAnchorEl(null)}
      >
        <MenuItem onClick={withMenuClose(onViewFile)} disableRipple>
          <VisibilityOutlinedIcon />
          {intl.formatMessage({ id: 'viewFile' })}
        </MenuItem>
        {onViewCase && (
          <MenuItem
            onClick={withMenuClose(onViewCase)}
            disableRipple
            disabled={!caseId}
          >
            <FolderOpenOutlinedIcon />
            {intl.formatMessage({ id: 'viewCase' })}
          </MenuItem>
        )}
        <MenuItem onClick={withMenuClose(handleOpenEditDrawer)} disableRipple>
          <EditAttributes />
          {intl.formatMessage({ id: 'editMetadata' })}
        </MenuItem>
        <MenuItem onClick={withMenuClose(onMove)} disableRipple>
          {caseId ? <DriveFileMoveOutlinedIcon /> : <AddToCase />}
          {intl.formatMessage({ id: caseId ? 'move' : 'addToCase' })}
        </MenuItem>
        <Divider />
        <MenuItem
          onClick={withMenuClose(onSendToRedact ?? (() => {}))}
          disableRipple
          disabled={!onSendToRedact}
        >
          <ExitToAppOutlinedIcon />
          {intl.formatMessage({ id: 'sendToRedact' })}
        </MenuItem>
        <MenuItem
          onClick={withMenuClose(onSendToTrack ?? (() => {}))}
          disableRipple
          disabled={!onSendToTrack}
        >
          <ExitToAppOutlinedIcon />
          {intl.formatMessage({ id: 'sendToTrack' })}
        </MenuItem>
        <Divider />
        <MenuItem onClick={withMenuClose(onDelete)} disableRipple>
          <DeleteOutlinedIcon />
          {intl.formatMessage({ id: 'delete' })}
        </MenuItem>
      </Menu>
    </div>
  );
};

interface FileCardProps {
  index: number;
  fileId: string;
  fileName: string;
  fileDuration: number;
  caseId?: string;
  folderId?: string;
  dateUploaded: string;
  thumbnailUrl: string;
  fileType?: string;
  blurred: boolean;
  isChecked: boolean;
  onCheck: (
    fileId: string,
    checked: boolean,
    event: ChangeEvent<HTMLInputElement>
  ) => void;
  onMove: () => void;
  onOpenEditMetadataDrawer: (rowId: string) => void;
  onViewFile: () => void;
  onViewCase?: () => void;
  onSendToRedact?: () => void;
  onSendToTrack?: () => void;
  onDelete: () => void;
  isDefaultThumbnail?: boolean;
  isPending?: boolean;
}

export default FileCard;
