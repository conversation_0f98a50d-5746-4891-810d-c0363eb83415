import { I18nTranslate } from '@i18n';
import Table, { Column, DataMap } from '@components/Table';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Icon<PERSON><PERSON>on, Tooltip } from '@mui/material';
import { ArrayOrSingle } from 'ts-essentials';
import dayjs from 'dayjs';
import { dispatchCustomEvent } from '@utils/helpers';
import { DownloadFile } from '@assets/icons';
import {
  convertFileType,
  FILE_TYPE,
  FILE_TYPE_ICON_MAP,
} from '@utils/files/convertFileType';
import { mockSharedFiles } from '../mockData';

export interface SharedFile {
  id: string;
  caseFile: string;
  uploadDate: string;
  fileSize: string;
  fileType: string;
  folderId: string;
}

const ShareFilesTable = () => {
  const intl = I18nTranslate.Intl();
  const navigate = useNavigate();
  const { shareId } = useParams<{ shareId: string }>();
  const [selected, setSelected] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<keyof SharedFile>('caseFile');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(50);

  // TODO: update sort and pagination when shared files data updated
  const { sortedData, totalResults } = useMemo(() => {
    const sorted = [...mockSharedFiles].sort((a, b) => {
      const valA = a[sortBy];
      const valB = b[sortBy];

      if (typeof valA === 'string' && typeof valB === 'string') {
        return sortDirection === 'asc'
          ? valA.localeCompare(valB)
          : valB.localeCompare(valA);
      }
      return 0;
    });
    return { sortedData: sorted, totalResults: sorted.length };
  }, [sortBy, sortDirection]);

  const paginatedData = useMemo(
    () =>
      sortedData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage),
    [sortedData, page, rowsPerPage]
  );

  const fileMap: DataMap<SharedFile> = useMemo(() => {
    const map: DataMap<SharedFile> = {};
    paginatedData.forEach((item, index) => {
      map[item.id] = { index, item };
    });
    return map;
  }, [paginatedData]);

  useEffect(() => {
    dispatchCustomEvent('enable-bulk-actions', {
      isEnabled: selectedFiles.length > 0,
    });
  }, [selectedFiles]);

  const handleSelect = (id: string) => {
    setSelected(id);
    setSelectedFiles([id]);
  };

  const handleDoubleClick = (fileId: string) => {
    navigate(`/share-manager/${shareId}/data-details/${fileId}`);
  };

  const handleSort = (key: string) => {
    const sortableKeys: (keyof SharedFile)[] = [
      'caseFile',
      'uploadDate',
      'fileSize',
      'fileType',
    ];
    if (!sortableKeys.includes(key as keyof SharedFile)) {
      return;
    }
    const isAsc = sortBy === key && sortDirection === 'asc';
    setSortDirection(isAsc ? 'desc' : 'asc');
    setSortBy(key as keyof SharedFile);
  };

  const onPageChange = (
    _event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {
    setPage(newPage);
    setSelected('');
    setSelectedFiles([]);
  };

  const onRowsPerPageChange = (event: ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    setSelected('');
    setSelectedFiles([]);
  };

  const renderCell = (
    value: ArrayOrSingle<string> | undefined,
    isToolTip?: boolean
  ) => {
    const cell = (
      <div className="file-table__table-cell">
        <span>{value || intl.formatMessage({ id: 'defaultEmpty' })}</span>
      </div>
    );
    return isToolTip ? (
      <Tooltip
        title={value as string}
        placement="bottom-start"
        disableHoverListener={!value}
      >
        {cell}
      </Tooltip>
    ) : (
      cell
    );
  };

  const fileColumns: Column<SharedFile>[] = [
    {
      header: '',
      width: '40px',
      render: ({ rowId }) => {
        const file = fileMap[rowId || '']?.item;
        if (!file) {
          return null;
        }
        const type: FILE_TYPE = convertFileType(file.fileType);
        const IconComponent = FILE_TYPE_ICON_MAP[type];
        return (
          <div
            style={{ display: 'flex', justifyContent: 'center', width: '100%' }}
          >
            <IconComponent />
          </div>
        );
      },
    },
    {
      field: 'caseFile',
      header: intl.formatMessage({ id: 'caseFile' }),
      width: '35%',
      render: ({ value }) => renderCell(value as string, true),
      isSortable: true,
    },
    {
      field: 'uploadDate',
      header: intl.formatMessage({ id: 'uploadDate' }),
      width: '25%',
      render: ({ value }) => dayjs(value as string).format('MM/DD/YYYY'),
      isSortable: true,
    },
    {
      field: 'fileSize',
      header: intl.formatMessage({ id: 'fileSize' }),
      width: '15%',
      isSortable: true,
    },
    {
      field: 'fileType',
      header: intl.formatMessage({ id: 'fileType' }),
      width: '15%',
      isSortable: true,
    },
    {
      header: '',
      width: '10%',
      render: ({ rowId }) => (
        <div className="menu-cell">
          <Tooltip title={intl.formatMessage({ id: 'download' })}>
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                if (rowId) {
                  // TODO: handle download file
                }
              }}
            >
              <DownloadFile />
            </IconButton>
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <Table<SharedFile>
      data={paginatedData}
      dataMap={fileMap}
      columns={fileColumns}
      row={{
        selected,
        handleSelect,
        handleDoubleClick,
      }}
      sort={{
        orderBy: sortBy,
        direction: sortDirection,
        handleSort,
      }}
      pagination={{
        page,
        count: totalResults,
        rowsPerPage,
        rowsPerPageOptions: [5, 10, 25, 50],
        onPageChange,
        onRowsPerPageChange,
      }}
      styles={{
        classname: 'file__table',
        isFixedTableLayout: true,
        noneCopyCell: true,
      }}
      extraProps={{}}
    />
  );
};

export default ShareFilesTable;
