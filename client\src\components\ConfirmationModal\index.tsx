import { I18nTranslate } from '@i18n';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
} from '@mui/material';
import { Theme } from '@mui/material/styles';
import { makeStyles } from 'tss-react/mui';

interface ConfirmationModalClasses {
  dialogTitle: string;
  dialogContent: string;
  dialogTitleText: string;
  dialogAction: string;
}

export interface IConfirmationModal {
  showConfirmationModal: boolean;
  closeConfirmationModal: () => void;
  onConfirmOkBtnClick: () => void;
  classes?: ConfirmationModalClasses;
  titleText?: string;
  mainMessageText?: string;
  backCancelButtonText?: string;
  confirmOkButtonText?: string;
}

export const ConfirmationModal = ({
  showConfirmationModal,
  closeConfirmationModal,
  onConfirmOkBtnClick,
  classes,
  titleText,
  mainMessageText,
  backCancelButtonText,
  confirmOkButtonText,
}: IConfirmationModal) => {
  const { classes: defaultClasses } = makeStyles()((theme: Theme) => ({
    dialogTitle: {
      padding: `${theme.spacing(6)} ${theme.spacing(6)} ${theme.spacing(1)}px !important `,
    },
    dialogContent: {
      padding: `${theme.spacing(1)} ${theme.spacing(6)} !important `,
    },
    dialogTitleText: {
      fontWeight: 600,
    },
    dialogAction: {
      padding: `${theme.spacing(4)} !important`,
    },
  }))();

  const modalClasses: ConfirmationModalClasses = {
    ...defaultClasses,
    ...classes,
  };

  return (
    <Dialog
      open={showConfirmationModal}
      onClose={closeConfirmationModal}
      maxWidth="xs"
      aria-labelledby="dialog-title"
      aria-describedby="dialog-description"
    >
      <DialogTitle
        id="dialog-title"
        data-test="data-center-importer-dialog-title"
        className={modalClasses?.dialogTitle}
      >
        <Typography variant="h2" className={modalClasses?.dialogTitleText}>
          {titleText || I18nTranslate.TranslateMessage('confirmClosePanel')}
        </Typography>
      </DialogTitle>
      <DialogContent className={modalClasses?.dialogContent}>
        <DialogContentText id="dialog-description">
          {mainMessageText ||
            I18nTranslate.TranslateMessage('areYouSureCloseAndLoseData')}
        </DialogContentText>
      </DialogContent>
      <DialogActions className={modalClasses?.dialogAction}>
        <Button
          onClick={closeConfirmationModal}
          variant="text"
          size="large"
          data-test="data-center-importer-dialog-back-btn"
        >
          {backCancelButtonText || I18nTranslate.TranslateMessage('back')}
        </Button>
        <Button
          onClick={onConfirmOkBtnClick}
          size="large"
          variant="contained"
          color="primary"
          data-test="data-center-importer-dialog-confirm-close-btn"
        >
          {confirmOkButtonText || I18nTranslate.TranslateMessage('closePanel')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationModal;
