/* cSpell:disable */
export const permsissionedCaseFolderData = {
  records: [
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::39d75135-adb7-4de2-b8b5-39f68a65934d::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: '39d75135-adb7-4de2-b8b5-39f68a65934d',
        name: 'Investigate Editor',
        description: 'Investigate Editor',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 2,
          records: [
            {
              member: {
                id: 'd7360c93-58d7-456e-9d2d-5dac0ce6b213',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
            {
              member: {
                id: '4def0d9d-caaf-4940-90a5-bf99d7631f1f',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:21:13.405Z',
    },
    {
      id: 'Folder::aaaa5ccb-af9a-41c4-ab36-3cbaf61acdd8::aaaa5135-adb7-4de2-b8b5-39f68a65934d::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'aaaa5ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: 'aaaa5135-adb7-4de2-b8b5-39f68a65934d',
        name: 'Group 2',
        description: 'Group 2 Investigate Editor',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 3,
          records: [
            {
              member: {
                id: 'd7360c93-58d7-456e-9d2d-5dac0ce6b213',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
            {
              member: {
                id: 'tucker-caaf-4940-90a5-bf99d7631f1f',
                email: '<EMAIL>',
                firstName: 'Tucker',
                lastName: 'Roth',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
            {
              member: {
                id: 'david-caaf-4940-90a5-bf99d7631f1f',
                email: '<EMAIL>',
                firstName: 'David',
                lastName: 'Skywalker',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:21:13.405Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::6ae09c0f-ddb7-497b-8280-e575a3e410de::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: '6ae09c0f-ddb7-497b-8280-e575a3e410de',
        name: 'group1',
        description: 'group1',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 0,
          records: [],
        },
      },
      createdAt: '2025-08-20T21:24:27.648Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::6c3aa30f-c720-4f00-afbc-7026f94f3665::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: '6c3aa30f-c720-4f00-afbc-7026f94f3665',
        name: 'Default Private Group for User 9e24877f-774a-4cc8-8d5c-849e14a26f51',
        description:
          'This group is created as the default group for User: 9e24877f-774a-4cc8-8d5c-849e14a26f51',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: '9e24877f-774a-4cc8-8d5c-849e14a26f51',
                email: '<EMAIL>',
                firstName: 'Daniel',
                lastName: 'Christiani',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:19:18.322Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::91083288-b420-4bf6-9b6b-a9c24c3bda5b::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: '91083288-b420-4bf6-9b6b-a9c24c3bda5b',
        name: 'Default Private Group for User e169e364-15f9-45ec-9064-c11f5eaa48f8',
        description:
          'This group is created as the default group for User: e169e364-15f9-45ec-9064-c11f5eaa48f8',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: 'e169e364-15f9-45ec-9064-c11f5eaa48f8',
                email: '<EMAIL>',
                firstName: 'Tucker',
                lastName: 'Roth',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:23:04.322Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::9aa0aa08-0950-4e49-b471-fdeceed12f89::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
        name: 'Default Private Group for User 9aa0aa08-0950-4e49-b471-fdeceed12f89',
        description:
          'This group is created as the default group for User: 9aa0aa08-0950-4e49-b471-fdeceed12f89',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T17:59:17.978Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::ff44cc7d-b194-47e7-83fd-ab43ae601465::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: 'ff44cc7d-b194-47e7-83fd-ab43ae601465',
        name: 'Default Private Group for User f2acb582-0c98-4892-850e-081a1d57489d',
        description:
          'This group is created as the default group for User: f2acb582-0c98-4892-850e-081a1d57489d',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: 'f2acb582-0c98-4892-850e-081a1d57489d',
                email: '<EMAIL>',
                firstName: 'Jordan',
                lastName: 'Ghidossi',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:04:33.029Z',
    },
  ],
};

export const allOrgAuthGroups = {
  records: [
    {
      id: '39d75135-adb7-4de2-b8b5-39f68a65934d',
      name: 'Investigate Editor',
      objectMemberType: 'AuthGroup',
      members: {
        records: [
          {
            member: {
              id: 'd7360c93-58d7-456e-9d2d-5dac0ce6b213',
              email: '<EMAIL>',
              firstName: 'Jia',
              lastName: 'Lu',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '4def0d9d-caaf-4940-90a5-bf99d7631f1f',
              email: '<EMAIL>',
              firstName: 'Jia',
              lastName: 'Lu',
              objectMemberType: 'User',
            },
          },
        ],
      },
    },
    {
      id: 'e25e2e79-1b98-48a8-82ba-8697fd89fa32',
      name: 'investigate - Investigate User',
      objectMemberType: 'AuthGroup',
      members: {
        records: [
          {
            member: {
              id: '332a32fd-aaa7-4ec6-a7d7-4335bbc9bfe2',
              email: '<EMAIL>',
              firstName: 'Phong',
              lastName: 'Vu',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '5a638922-bc0a-46ca-b43d-b12363888221',
              email: '<EMAIL>',
              firstName: 'Stephen',
              lastName: 'Tran',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '238c9f13-ce9e-45d0-9046-ecae1eaee888',
              email: '<EMAIL>',
              firstName: 'Tucker1',
              lastName: 'Roth',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'e169e364-15f9-45ec-9064-c11f5eaa48f8',
              email: '<EMAIL>',
              firstName: 'Tucker',
              lastName: 'Roth',
              objectMemberType: 'User',
            },
          },
        ],
      },
    },
    {
      id: '5d352a4c-9d49-44a7-84ba-4bce94305477',
      name: 'backend',
      objectMemberType: 'AuthGroup',
      members: {
        records: [
          {
            member: {
              id: '46ad996c-68fc-4aa4-85a1-6cf1b8205486',
              email: '<EMAIL>',
              firstName: 'Jia',
              lastName: 'Lu',
              objectMemberType: 'User',
            },
          },
        ],
      },
    },
    {
      id: '94bf8e12-e67c-49a8-903c-198dd964e6ea',
      name: 'track_v_2 - Default App Role',
      objectMemberType: 'AuthGroup',
      members: {
        records: [
          {
            member: {
              id: '9e24877f-774a-4cc8-8d5c-849e14a26f51',
              email: '<EMAIL>',
              firstName: 'Daniel',
              lastName: 'Christiani',
              objectMemberType: 'User',
            },
          },
        ],
      },
    },
    {
      id: 'a30297d7-6f99-429c-901a-8965c511a655',
      name: 'Investigate Test Administrators',
      objectMemberType: 'AuthGroup',
      members: {
        records: [
          {
            member: {
              id: 'ea3d773d-2ab4-4769-a731-e88e3faa4e56',
              email: '<EMAIL>',
              firstName: 'Ben',
              lastName: 'Ha',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '2637c26f-b742-4e42-9bb5-2967800ec620',
              email: '<EMAIL>',
              firstName: 'Bradley',
              lastName: 'Worrell-Smith',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '83950a3b-7eec-44be-9527-470ffb9edfcb',
              email: '<EMAIL>',
              firstName: 'Brad',
              lastName: 'Smith',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '9e24877f-774a-4cc8-8d5c-849e14a26f51',
              email: '<EMAIL>',
              firstName: 'Daniel',
              lastName: 'Christiani',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'fbf8736f-ce3f-4f6e-bbfb-2a9b9eb4f80a',
              email: '<EMAIL>',
              firstName: 'Derek',
              lastName: 'Ody',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'd313729b-6607-434b-9495-aae130c77f23',
              email: '<EMAIL>',
              firstName: 'Derrick',
              lastName: 'Roos',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '21791753-6eae-4620-bd43-1c8cbc0e2b98',
              email: '<EMAIL>',
              firstName: 'Jessica',
              lastName: 'Alessandro',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '33d2c09e-2c70-4a16-8040-c201317b35e2',
              email: '<EMAIL>',
              firstName: 'Jon',
              lastName: 'Gacek',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '40ed179c-289a-4c5f-8302-749cec1ec31f',
              email: '<EMAIL>',
              firstName: 'Jordan',
              lastName: 'Ghidossi',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
              email: '<EMAIL>',
              firstName: 'Jia',
              lastName: 'Lu',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '0cb71fe0-2a0f-4331-8bda-81b53ed8a6e9',
              email: '<EMAIL>',
              firstName: 'Kelly',
              lastName: 'Inabnett',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'c4fc40da-ff41-4089-823a-d3b9b09f5531',
              email: '<EMAIL>',
              firstName: 'Nicole',
              lastName: 'Jayne',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'bffecfba-690a-417b-91df-42bf6b7edc35',
              email: '<EMAIL>',
              firstName: 'Pat',
              lastName: 'Moore',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '2a1798ff-cb8e-4f8e-ae9d-7dcd119892c4',
              email: '<EMAIL>',
              firstName: 'Rob',
              lastName: 'Gerber',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '5a638922-bc0a-46ca-b43d-b12363888221',
              email: '<EMAIL>',
              firstName: 'Stephen',
              lastName: 'Tran',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'd4de84fa-c9ce-4608-b7f1-b5327d46326a',
              email: '<EMAIL>',
              firstName: 'Tim',
              lastName: 'Camara',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '1e875f3f-b14f-4a64-aa09-7e3f99b6cd36',
              email: '<EMAIL>',
              firstName: 'Tung',
              lastName: 'Phan',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'cacb3b78-c30f-48cf-9b0f-bbd10c65f133',
              email: '<EMAIL>',
              firstName: 'Tucker',
              lastName: 'Roth',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'e169e364-15f9-45ec-9064-c11f5eaa48f8',
              email: '<EMAIL>',
              firstName: 'Tucker',
              lastName: 'Roth',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '352fc412-1003-402c-8b29-5f5835f466c7',
              email: '<EMAIL>',
              firstName: 'Tarah',
              lastName: 'Sreboth',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'e41434a5-b6b1-4c6d-8fa9-c069e52f74ef',
              email: '<EMAIL>',
              firstName: 'Victoria',
              lastName: 'Dickson',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '66c0515f-a43c-4dd4-86e4-b1e022337718',
              email: '<EMAIL>',
              firstName: 'Yumi',
              lastName: 'Nguyen',
              objectMemberType: 'User',
            },
          },
        ],
      },
    },
    {
      id: '5f68b4c9-d838-4ae5-ba04-6adb3e9734bc',
      name: 'Investigate Admin',
      objectMemberType: 'AuthGroup',
      members: {
        records: [
          {
            member: {
              id: 'ea3d773d-2ab4-4769-a731-e88e3faa4e56',
              email: '<EMAIL>',
              firstName: 'Ben',
              lastName: 'Ha',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '2637c26f-b742-4e42-9bb5-2967800ec620',
              email: '<EMAIL>',
              firstName: 'Bradley',
              lastName: 'Worrell-Smith',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '83950a3b-7eec-44be-9527-470ffb9edfcb',
              email: '<EMAIL>',
              firstName: 'Brad',
              lastName: 'Smith',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '9e24877f-774a-4cc8-8d5c-849e14a26f51',
              email: '<EMAIL>',
              firstName: 'Daniel',
              lastName: 'Christiani',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'd313729b-6607-434b-9495-aae130c77f23',
              email: '<EMAIL>',
              firstName: 'Derrick',
              lastName: 'Roos',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '21791753-6eae-4620-bd43-1c8cbc0e2b98',
              email: '<EMAIL>',
              firstName: 'Jessica',
              lastName: 'Alessandro',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '40ed179c-289a-4c5f-8302-749cec1ec31f',
              email: '<EMAIL>',
              firstName: 'Jordan',
              lastName: 'Ghidossi',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
              email: '<EMAIL>',
              firstName: 'Jia',
              lastName: 'Lu',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '0cb71fe0-2a0f-4331-8bda-81b53ed8a6e9',
              email: '<EMAIL>',
              firstName: 'Kelly',
              lastName: 'Inabnett',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'c4fc40da-ff41-4089-823a-d3b9b09f5531',
              email: '<EMAIL>',
              firstName: 'Nicole',
              lastName: 'Jayne',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'bffecfba-690a-417b-91df-42bf6b7edc35',
              email: '<EMAIL>',
              firstName: 'Pat',
              lastName: 'Moore',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '2a1798ff-cb8e-4f8e-ae9d-7dcd119892c4',
              email: '<EMAIL>',
              firstName: 'Rob',
              lastName: 'Gerber',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '5a638922-bc0a-46ca-b43d-b12363888221',
              email: '<EMAIL>',
              firstName: 'Stephen',
              lastName: 'Tran',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'cacb3b78-c30f-48cf-9b0f-bbd10c65f133',
              email: '<EMAIL>',
              firstName: 'Tucker',
              lastName: 'Roth',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '352fc412-1003-402c-8b29-5f5835f466c7',
              email: '<EMAIL>',
              firstName: 'Tarah',
              lastName: 'Sreboth',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: '534e64ca-a0c7-45c7-a8e5-0a7eda490777',
              email: '<EMAIL>',
              firstName: 'Victoria',
              lastName: 'Dickson',
              objectMemberType: 'User',
            },
          },
          {
            member: {
              id: 'e41434a5-b6b1-4c6d-8fa9-c069e52f74ef',
              email: '<EMAIL>',
              firstName: 'Victoria',
              lastName: 'Dickson',
              objectMemberType: 'User',
            },
          },
        ],
      },
    },
    {
      id: 'cb724553-ece2-4bf1-abea-448335514fc4',
      name: 'Investigate Test Users',
      objectMemberType: 'AuthGroup',
      members: {
        records: [
          {
            member: {
              id: 'd7360c93-58d7-456e-9d2d-5dac0ce6b213',
              email: '<EMAIL>',
              firstName: 'Jia',
              lastName: 'Lu',
              objectMemberType: 'User',
            },
          },
        ],
      },
    },
    {
      id: 'd0b351ca-b9da-4fd1-927c-8cf64aa4a730',
      name: 'null - Lance Test Role',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '3e2737ad-732b-4f72-b3e0-0dc8784725f6',
      name: 'Viewer Access g',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '6ae09c0f-ddb7-497b-8280-e575a3e410de',
      name: 'group1',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '4ba505c5-83c8-4380-a82a-afa0e10deabc',
      name: 'track_v_2 - Default App Access',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '26d1dfef-a8aa-43fa-bcfe-2d9f81e25b13',
      name: 'Investigate Create',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '2224c787-b7c0-4e8c-931c-637a3f7e1f7e',
      name: 'groupsdo1',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: 'e885fa00-e348-412c-bfba-0fe720851391',
      name: 'APITOKEN:4603d031-3ad9-4e56-a956-3ee9c4fb26cf',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '65bfefc3-64cd-4a05-b02e-081d6a8c35ce',
      name: 'track_v_2 - Track Admin',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '53ebc073-ee6c-499b-af5d-549dc6b61dba',
      name: 'group2',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '328c873a-c118-4991-b92d-d6282e379cf2',
      name: 'APITOKEN:244fb044-5a70-40f9-bf39-3eea0bdf6153',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '3c2db194-2b0c-4ff5-84e4-ed59d1a042c9',
      name: 'APITOKEN:32f44094-9539-4296-8651-3f1e61ef5094',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: 'b86c219c-5289-46d4-adcb-375e04823a4a',
      name: 'tuckergroup1',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: 'a0cc5be1-271b-4dbc-aac1-b88e7074e649',
      name: 'Investigate Viewer',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: 'ab156353-39ec-42e2-914e-36a862027ff8',
      name: 'groupsdo2',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: 'b1e9cea8-da11-44f1-bc4a-d828e4b9829c',
      name: 'APITOKEN:26cb8b34-b53a-459a-8668-e42a5da74fbe',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: 'b1bfe9ef-8a69-4908-9a88-41aa3e2199fb',
      name: 'Investigate Read',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: 'cb253adc-be28-41b2-b748-1195979e2986',
      name: 'APITOKEN:4060b78e-0da1-4fb1-9c3f-4ff2df1244c7',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
    {
      id: '4e833d45-baf1-4b14-a2e9-3116755473d7',
      name: 'investigate - Default App Access',
      objectMemberType: 'AuthGroup',
      members: {
        records: [],
      },
    },
  ],
};

export const allOrgUsers = {
  records: [
    {
      id: '7640c786-3ea5-48ad-a738-e133a1692b3c',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'SystemUser',
      lastName: 'AppEventUser_Investigate',
      email:
        '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'ea3d773d-2ab4-4769-a731-e88e3faa4e56',
      imageUrl:
        'https://s3.amazonaws.com/dev-veritone-ugc/avatar%2FtgmwfLWOTSW8vBTnhh9P_Ben%20Ha%20-%20Profile%20Photo.jpg.png',
      name: '<EMAIL>',
      firstName: 'Ben',
      lastName: 'Ha',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-19T02:52:24.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'ee745ca5-4151-4db6-a5e6-50d0e8af85f5',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Christian',
      lastName: 'Preston',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-07-29T13:35:24.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '9e24877f-774a-4cc8-8d5c-849e14a26f51',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Daniel',
      lastName: 'Christiani',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-21T15:19:15.000Z',
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: '8f07e84f-693c-4821-9f44-f4966fa304d1',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'EditorRole',
      lastName: 'Test',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: 'ee040b88-d31b-4927-932d-6fefcf819b95',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Daniel',
      lastName: 'Christiani',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-07-16T19:59:41.000Z',
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: '75c663fa-1688-4aa8-ae1f-8f1b9680c73b',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Daniel',
      lastName: 'Christiani',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: 'd313729b-6607-434b-9495-aae130c77f23',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Derrick',
      lastName: 'Roos',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: '07d46b45-a98f-4660-a1ac-4990b1408c7e',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Edward',
      lastName: 'Burnell',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: 'd8285ec1-eb46-416c-bac1-68c4dd95214d',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Hop',
      lastName: 'Vu',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: '33d2c09e-2c70-4a16-8040-c201317b35e2',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Jon',
      lastName: 'Gacek',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-06-05T21:52:20.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'f2acb582-0c98-4892-850e-081a1d57489d',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Jordan',
      lastName: 'Ghidossi',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: '40ed179c-289a-4c5f-8302-749cec1ec31f',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Jordan',
      lastName: 'Ghidossi',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-21T15:19:24.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '46ad996c-68fc-4aa4-85a1-6cf1b8205486',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Jia',
      lastName: 'Lu',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-19T18:41:25.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '4dbf8f7f-8d59-4df2-83e7-f2803b78139b',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: '',
      lastName: '',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-06-11T20:55:12.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '06a82afc-64b8-4462-82a4-985b24f064bd',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: '',
      lastName: '',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-01-28T00:44:59.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '1ce691f2-f392-493c-9b29-d5204d07f3fb',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: '',
      lastName: '',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-04-28T17:50:48.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'f20a5961-69aa-48ce-bb01-cdeb4a5fefa6',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Jia',
      lastName: 'Lu',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-06-11T17:26:42.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'd7360c93-58d7-456e-9d2d-5dac0ce6b213',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Jia',
      lastName: 'Lu',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-06-11T20:24:54.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Jia',
      lastName: 'Lu',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-20T23:16:12.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'df560600-0593-416e-9a08-c07120ac3279',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Jozsef',
      lastName: 'Szakacs',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-04-01T11:20:14.000Z',
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: 'bdae3c16-9cd5-4cca-a346-d70f29a001da',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Kim',
      lastName: 'Myers',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-04-21T20:03:25.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '162cfe53-**************-6295325ea99f',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Luke',
      lastName: 'Pierce',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: '7ad29340-997f-41be-a5b7-e2de977f24a8',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Mert',
      lastName: 'Nacakgedigi',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-05-12T21:21:52.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'c4fc40da-ff41-4089-823a-d3b9b09f5531',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Nicole',
      lastName: 'Jayne',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-11T20:26:32.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'bffecfba-690a-417b-91df-42bf6b7edc35',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Pat',
      lastName: 'Moore',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-19T16:53:33.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '332a32fd-aaa7-4ec6-a7d7-4335bbc9bfe2',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Phong',
      lastName: 'Vu',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'afba0eb3-6996-4dc3-968d-2b55741103ec',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Robert',
      lastName: 'Klaas',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-07-18T00:25:31.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '8fc3b3fe-48a7-4f56-a615-8cfbd8e46e01',
      imageUrl:
        'https://s3.amazonaws.com/prod-api.veritone.com/7682/other/2022/3/3/_/Screen%20Shot%202022-04-13%20at%2012.41.58%20PM-17-3-249_0385ca1d-f499-4db6-866d-4e794c22e68e.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAQMR5VATUCNBZXPCT%2F20250821%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250821T163616Z&X-Amz-Expires=1200&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEKT%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMSJHMEUCIQCvQRh7BWDzcMz3jQdLAoDWlBr8XvOFLbdEJDb1yjq4MQIgcK56UzYSTJ%2FIWBlgLt4ncNZOwl18jSq6eyrXPrQLtpQqvAUI7f%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgwwMjY5NzI4NDkzODQiDAukobNA05551nyYsiqQBasiZCNenHw9jgSO3zIyZIGMoeoLt%2Bt95CVNqjjl95YxalRXodZSF1ovC2mHwHsD8b9XFQ0squ7i4FRf99nOWAd9q%2BjoPRbYQfZf1zMWqOVA%2BYM1%2F%2BTwbSp%2B2ZWxGlJI6WV8rf%2F5NDgkGAQKbxDflbaLvsdW70qM00%2FPY5HSdc95QdSpYxg25znjf8XDeBlrZjlHQmO%2FzWo6GzN%2FZfO%2F2dUJw5P2HZONbM6HbuJ6J%2Bdcb0exv5jKoDVkyNhei%2BXtSWwAS4vLsYgdR8mE%2BD7tX8OVC7oMGYErd0Iz3NDKR1DTkIWmhpWr9qw8hpv5bYx9YqXLHT24ZSyIxKMxYwtd6rchHNMwRdafOgSS8LYon3BDRaxopa9V%2BD4S%2B9IciZD4vADoVfm0BJi8ntaRSrWSNbWl2xi2ZQFjCWNBtTKbpO7ViQbFOrUNWEOS4bVNxF30Huao4XreFtB7z6KSdov%2BYoW1SCRPlO8ZFvVHJSmo98MvZgfSCJkDTVtX6K%2Bu9JhgY8W1HI1D1K%2FGzAeZ4%2BCI3RnOk0yolpTCW%2F6RHIZEBQRRP48xYvs6IbS4asOsehDZ6A3ZKF0lTnOujjMTBBWoxIORRxrrwP97iB%2F2n%2Fp4uzfI%2BCoxjdRHH7W9Y0tVguBQDlwzwnZZ2AuXaLEtFGVFip7R2BLPjz6fllIKfxmswZVbEHdhMKaY5YNMX98vCsKSevu7%2FlaInhK1TmLac6gKG90LT2Tiju0xVAWuYcfkwDgrp4nj7oL%2F5iuFKCZ%2FO6exa%2F1I9yAEdvp3%2BNFUFmma8v5J15Yh7mlRgD3sEc4k97%2F743E7dtjAN%2F%2FMPhcXDxeZXooimsH4sEiAxxID0aD4mgdTrmckXFJCKT92OudvGrdzMOSJnMUGOrEBqWvli%2BsYAkQdXhNMYpaVfBIbRjzpUSIVnQQGUurM2f8T29m7TCMtBne3DURJEmlhN%2F%2FYWR6gFbTYPmWVs2T9o8jljpVRBSQ5I51x%2FP4QJV6secdaJdmvxeLs67Vj8sAsNoBjHifWMS9ZXRVbbBNsiEiFAzq7HzDhnIv1XTjwXSdsbLFVC2s5SObQnsYCcp6dhCt47Dih6%2FcWGIxg%2BIfqWgZmYbp9s7g%2BU4EKGBXTFUPB&X-Amz-Signature=d45de659558259396def00fe9d0dc3bff5ed5fc7c30eb805b13f7b5a7961036e&X-Amz-SignedHeaders=host',
      name: '<EMAIL>',
      firstName: 'Sierra',
      lastName: 'Fong',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'deactivated',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: 'b1f59aa2-8af4-48c8-b156-897e8dda1100',
      imageUrl: 'undefined',
      name: '<EMAIL>',
      firstName: 's',
      lastName: 'minkov',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-07-10T17:45:09.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '5a638922-bc0a-46ca-b43d-b12363888221',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Stephen',
      lastName: 'Tran',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-19T19:33:24.000Z',
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: 'd4de84fa-c9ce-4608-b7f1-b5327d46326a',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Tim',
      lastName: 'Camara',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-20T21:17:30.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '5ce5bf9d-c289-475b-ae50-f48f9f07e1af',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Tim',
      lastName: 'Kay',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-13T14:43:32.000Z',
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: '1e875f3f-b14f-4a64-aa09-7e3f99b6cd36',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Tung',
      lastName: 'Phan',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-06-11T07:16:32.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '34bba39f-21d9-4e1a-9e1c-7bbc76113e41',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: '',
      lastName: '',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-14T14:36:01.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '80fc2e49-7712-4457-8bf8-38cf25e09238',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: '',
      lastName: '',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: '238c9f13-ce9e-45d0-9046-ecae1eaee888',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Tucker1',
      lastName: 'Roth',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'cacb3b78-c30f-48cf-9b0f-bbd10c65f133',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Tucker',
      lastName: 'Roth',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-21T14:55:28.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'e169e364-15f9-45ec-9064-c11f5eaa48f8',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Tucker',
      lastName: 'Roth',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-18T16:41:00.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: 'ad141987-5246-4c65-8c55-3a489be18ac8',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: '',
      lastName: '',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-05-01T14:21:44.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '352fc412-1003-402c-8b29-5f5835f466c7',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Tarah',
      lastName: 'Sreboth',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-14T18:28:38.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '66ff6ff0-67f9-46b2-b2fc-e67c33443013',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Thomas',
      lastName: 'Walpole',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: 'aeabf2b5-7503-4b2f-b3a5-a4ebf8434577',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Thomas',
      lastName: 'Walpole',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-06-26T17:24:27.000Z',
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: 'e41434a5-b6b1-4c6d-8fa9-c069e52f74ef',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Victoria',
      lastName: 'Dickson',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-08T20:47:43.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
    {
      id: '24e1738b-2c19-46cd-9d3c-b8d259fc2f31',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Yao',
      lastName: 'Gong',
      email: '<EMAIL>',
      lastLoginDateTime: null,
      status: 'active',
      organizationId: '46571',
      organization: {
        guid: '62de9fb8-5ee8-469c-b31f-26700c8cabb6',
      },
      memberType: 'User',
    },
    {
      id: '66c0515f-a43c-4dd4-86e4-b1e022337718',
      imageUrl: null,
      name: '<EMAIL>',
      firstName: 'Yumi',
      lastName: 'Nguyen',
      email: '<EMAIL>',
      lastLoginDateTime: '2025-08-21T06:27:04.000Z',
      status: 'active',
      organizationId: '49856',
      organization: {
        guid: '71459e59-e555-4c3d-a391-9adb79a6576b',
      },
      memberType: 'User',
    },
  ],
};

export const mockOrgPermissionSets = [
  {
    id: '29b8bb78-**************-786ce3d9e5c9',
    name: 'Investigate Admin',
    description: 'Investigate Admin Permission Set',
  },
  {
    id: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
    name: 'Investigate Editor',
    description: 'Investigate Editor Permission Set',
  },
  {
    id: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
    name: 'Investigate Viewer',
    description: 'Investigate Viewer Permission Set',
  },
  {
    id: '6632a9f9-4745-4c52-95ca-2208d5cf3ed4',
    name: 'Investigate Read Permission Set ID',
    description: 'Investigate Read Permission Set ID',
  },
  {
    id: '5701543d-f14a-456c-80dd-************',
    name: 'investigate - Investigate User',
    description: 'Permission set for App role: investigate - Investigate User',
  },
];

export const permsissionedCaseFolderDataWithoutTucker = {
  records: [
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::39d75135-adb7-4de2-b8b5-39f68a65934d::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: '39d75135-adb7-4de2-b8b5-39f68a65934d',
        name: 'Investigate Editor',
        description: 'Investigate Editor',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 2,
          records: [
            {
              member: {
                id: 'd7360c93-58d7-456e-9d2d-5dac0ce6b213',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
            {
              member: {
                id: '4def0d9d-caaf-4940-90a5-bf99d7631f1f',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:21:13.405Z',
    },
    {
      id: 'Folder::aaaa5ccb-af9a-41c4-ab36-3cbaf61acdd8::aaaa5135-adb7-4de2-b8b5-39f68a65934d::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'aaaa5ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: 'aaaa5135-adb7-4de2-b8b5-39f68a65934d',
        name: 'Group 2',
        description: 'Group 2 Investigate Editor',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 3,
          records: [
            {
              member: {
                id: 'd7360c93-58d7-456e-9d2d-5dac0ce6b213',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
            {
              member: {
                id: 'tucker-caaf-4940-90a5-bf99d7631f1f',
                email: '<EMAIL>',
                firstName: 'Tucker',
                lastName: 'Roth',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
            {
              member: {
                id: 'david-caaf-4940-90a5-bf99d7631f1f',
                email: '<EMAIL>',
                firstName: 'David',
                lastName: 'Skywalker',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:21:13.405Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::6ae09c0f-ddb7-497b-8280-e575a3e410de::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: '6ae09c0f-ddb7-497b-8280-e575a3e410de',
        name: 'group1',
        description: 'group1',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 0,
          records: [],
        },
      },
      createdAt: '2025-08-20T21:24:27.648Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::6c3aa30f-c720-4f00-afbc-7026f94f3665::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: '6c3aa30f-c720-4f00-afbc-7026f94f3665',
        name: 'Default Private Group for User 9e24877f-774a-4cc8-8d5c-849e14a26f51',
        description:
          'This group is created as the default group for User: 9e24877f-774a-4cc8-8d5c-849e14a26f51',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: '9e24877f-774a-4cc8-8d5c-849e14a26f51',
                email: '<EMAIL>',
                firstName: 'Daniel',
                lastName: 'Christiani',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:19:18.322Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::9aa0aa08-0950-4e49-b471-fdeceed12f89::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
        name: 'Default Private Group for User 9aa0aa08-0950-4e49-b471-fdeceed12f89',
        description:
          'This group is created as the default group for User: 9aa0aa08-0950-4e49-b471-fdeceed12f89',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T17:59:17.978Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::ff44cc7d-b194-47e7-83fd-ab43ae601465::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: 'ff44cc7d-b194-47e7-83fd-ab43ae601465',
        name: 'Default Private Group for User f2acb582-0c98-4892-850e-081a1d57489d',
        description:
          'This group is created as the default group for User: f2acb582-0c98-4892-850e-081a1d57489d',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: 'f2acb582-0c98-4892-850e-081a1d57489d',
                email: '<EMAIL>',
                firstName: 'Jordan',
                lastName: 'Ghidossi',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:04:33.029Z',
    },
  ],
};

export const permsissionedCaseFolderDataWithBenHaAdded = {
  records: [
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::39d75135-adb7-4de2-b8b5-39f68a65934d::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: '39d75135-adb7-4de2-b8b5-39f68a65934d',
        name: 'Investigate Editor',
        description: 'Investigate Editor',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 2,
          records: [
            {
              member: {
                id: 'd7360c93-58d7-456e-9d2d-5dac0ce6b213',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
            {
              member: {
                id: '4def0d9d-caaf-4940-90a5-bf99d7631f1f',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:21:13.405Z',
    },
    {
      id: 'Folder::aaaa5ccb-af9a-41c4-ab36-3cbaf61acdd8::aaaa5135-adb7-4de2-b8b5-39f68a65934d::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'aaaa5ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: 'aaaa5135-adb7-4de2-b8b5-39f68a65934d',
        name: 'Group 2',
        description: 'Group 2 Investigate Editor',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 3,
          records: [
            {
              member: {
                id: 'd7360c93-58d7-456e-9d2d-5dac0ce6b213',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
            {
              member: {
                id: 'tucker-caaf-4940-90a5-bf99d7631f1f',
                email: '<EMAIL>',
                firstName: 'Tucker',
                lastName: 'Roth',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
            {
              member: {
                id: 'david-caaf-4940-90a5-bf99d7631f1f',
                email: '<EMAIL>',
                firstName: 'David',
                lastName: 'Skywalker',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:21:13.405Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::6ae09c0f-ddb7-497b-8280-e575a3e410de::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: '6ae09c0f-ddb7-497b-8280-e575a3e410de',
        name: 'group1',
        description: 'group1',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 0,
          records: [],
        },
      },
      createdAt: '2025-08-20T21:24:27.648Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::5a3b377f-ce9d-4b76-81e5-a220f97dfc8a::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: '5a3b377f-ce9d-4b76-81e5-a220f97dfc8a',
        name: 'Default Private Group for User ea3d773d-2ab4-4769-a731-e88e3faa4e56',
        description:
          'This group is created as the default group for User: ea3d773d-2ab4-4769-a731-e88e3faa4e56',
        objectMemberType: 'AuthGroup',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: 'ea3d773d-2ab4-4769-a731-e88e3faa4e56',
                email: '<EMAIL>',
                firstName: 'Ben',
                lastName: 'Ha',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
        memberType: 'Group',
      },
      createdAt: '2025-08-25T15:35:10.492Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::6c3aa30f-c720-4f00-afbc-7026f94f3665::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: '6c3aa30f-c720-4f00-afbc-7026f94f3665',
        name: 'Default Private Group for User 9e24877f-774a-4cc8-8d5c-849e14a26f51',
        description:
          'This group is created as the default group for User: 9e24877f-774a-4cc8-8d5c-849e14a26f51',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: '9e24877f-774a-4cc8-8d5c-849e14a26f51',
                email: '<EMAIL>',
                firstName: 'Daniel',
                lastName: 'Christiani',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:19:18.322Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::91083288-b420-4bf6-9b6b-a9c24c3bda5b::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: '91083288-b420-4bf6-9b6b-a9c24c3bda5b',
        name: 'Default Private Group for User e169e364-15f9-45ec-9064-c11f5eaa48f8',
        description:
          'This group is created as the default group for User: e169e364-15f9-45ec-9064-c11f5eaa48f8',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: 'e169e364-15f9-45ec-9064-c11f5eaa48f8',
                email: '<EMAIL>',
                firstName: 'Tucker',
                lastName: 'Roth',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:23:04.322Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::9aa0aa08-0950-4e49-b471-fdeceed12f89::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: '88c05684-33e8-491b-80b3-fdb143ab2b1e',
        permissionName: 'Investigate Editor',
        permissionDescription: 'Investigate Editor Permission Set',
      },
      member: {
        id: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
        name: 'Default Private Group for User 9aa0aa08-0950-4e49-b471-fdeceed12f89',
        description:
          'This group is created as the default group for User: 9aa0aa08-0950-4e49-b471-fdeceed12f89',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
                email: '<EMAIL>',
                firstName: 'Jia',
                lastName: 'Lu',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T17:59:17.978Z',
    },
    {
      id: 'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::ff44cc7d-b194-47e7-83fd-ab43ae601465::a43f19e4-04aa-4549-8db9-4f22b7258566',
      objectID: 'd2315ccb-af9a-41c4-ab36-3cbaf61acdd8',
      objectType: 'Folder',
      permissionSet: {
        permissionId: 'a43f19e4-04aa-4549-8db9-4f22b7258566',
        permissionName: 'Investigate Viewer',
        permissionDescription: 'Investigate Viewer Permission Set',
      },
      member: {
        id: 'ff44cc7d-b194-47e7-83fd-ab43ae601465',
        name: 'Default Private Group for User f2acb582-0c98-4892-850e-081a1d57489d',
        description:
          'This group is created as the default group for User: f2acb582-0c98-4892-850e-081a1d57489d',
        objectMemberType: 'AuthGroup',
        memberType: 'Group',
        members: {
          count: 1,
          records: [
            {
              member: {
                id: 'f2acb582-0c98-4892-850e-081a1d57489d',
                email: '<EMAIL>',
                firstName: 'Jordan',
                lastName: 'Ghidossi',
                objectMemberType: 'User',
                memberType: 'User',
              },
            },
          ],
        },
      },
      createdAt: '2025-08-20T21:04:33.029Z',
    },
  ],
};
