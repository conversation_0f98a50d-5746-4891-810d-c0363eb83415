import { configureAppStore } from '@store/index';
import {
  CaseDetailSliceState,
  initialState as caseDetailInitialState,
} from '@store/modules/caseDetail/slice';
import { screen, waitFor, within } from '@testing-library/dom';
import userEvent from '@testing-library/user-event';
import { ViewType } from '@utils/local-storage/viewTypes';
import { cloneDeep } from 'lodash';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router';
import { describe, expect, it, vi } from 'vitest';
import FileTable from '.';
import { render } from '../../../../test/render';
import { VFile } from '@shared-types/types';
import { toggleOpenEditDrawer } from '@store/modules/metadata/slice';

const mockVFiles: VFile[] = [
  {
    id: '**********',
    fileName: 'file1.mp4',
    fileType: 'video',
    createdTime: '2025-04-05T03:59:09.000Z',
    duration: 120,
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'User A',
    updatedTime: '2025-04-05T04:10:49.000Z',
    description: 'Description 1',
    programLiveImage: 'img1.jpg',
    fileStatus: 'processed',
    sourceName: 'Source A',
    evidenceType: 'Body Worn Camera',
  },
  {
    id: '22222222',
    fileName: 'file2.mp3',
    fileType: 'audio',
    createdTime: '2025-04-06T03:59:09.000Z',
    duration: 180,
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'User B',
    updatedTime: '2025-04-06T04:10:49.000Z',
    description: 'Description 2',
    programLiveImage: 'img2.jpg',
    fileStatus: 'pending',
    sourceName: 'Source B',
    evidenceType: '911 Call Recording',
  },
  {
    id: '33333333',
    fileName: 'file3.jpg',
    fileType: 'image',
    createdTime: '2025-04-07T03:59:09.000Z',
    duration: -1,
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'User C',
    updatedTime: '2025-04-07T04:10:49.000Z',
    description: 'Description 3',
    programLiveImage: 'img3.jpg',
    fileStatus: 'error',
    sourceName: 'Source C',
    evidenceType: 'Crime Scene Photo',
  },
  {
    id: '444444444',
    fileName: 'file4.pdf',
    fileType: 'doc',
    createdTime: '2025-04-08T03:59:09.000Z',
    duration: -1,
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'User D',
    updatedTime: '2025-04-08T04:10:49.000Z',
    description: 'Description 4',
    programLiveImage: 'img4.jpg',
    fileStatus: 'processed',
    sourceName: 'Source D',
    evidenceType: 'Arrest Report',
  },
  {
    id: '555555555',
    fileName: 'file5.txt',
    fileType: 'doc',
    createdTime: '2025-04-09T03:59:09.000Z',
    duration: -1,
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'User E',
    updatedTime: '2025-04-09T04:10:49.000Z',
    description: 'Description 5',
    programLiveImage: 'img5.jpg',
    fileStatus: 'processed',
    sourceName: 'Source E',
    evidenceType: 'Un-Assigned',
  },
];

const initialState: {
  caseDetail: CaseDetailSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  caseDetail: {
    ...caseDetailInitialState,
    files: {
      status: 'complete',
      error: '',
      data: {
        results: mockVFiles,
        totalResults: mockVFiles.length,
        limit: 50,
        from: 0,
        to: mockVFiles.length,
      },
    },
    viewType: ViewType.LIST,
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    dataRegistry: {
      caseRegistry: { name: 'caseRegistryName123', id: 'caseRegistryId123' },
      statusRegistry: {
        name: 'statusRegistryName123',
        id: 'statusRegistryId123',
      },
      tagRegistry: { name: 'tagRegistryName123', id: 'tagRegistryId123' },
      evidenceTypeRegistry: {
        name: 'evidenceTypeRegistryName123',
        id: 'evidenceTypeRegistryId123',
      },
    },
    nodeEnv: 'dev',
  },
  auth: { sessionToken: 'sessionToken123' },
};

const defaultProps = {
  selected: '',
  handleSelect: vi.fn(),
  handleDoubleClick: vi.fn(),
  handleSendToRedact: vi.fn(),
  handleSendToTrack: vi.fn(),
  handleUploadFile: vi.fn(),
  setSelectedFolderId: vi.fn(),
  selectedFolderId: 'folder_1',
  setSelected: vi.fn(),
  pendingDeleteIds: [],
  setPendingDeleteIds: vi.fn(),
  pendingMoveFileIds: [],
  classname: 'file__table',
};

const getCheckbox = (dataTestId: string) =>
  within(screen.getByTestId(dataTestId)).getByRole<HTMLInputElement>(
    'checkbox'
  );

describe('File Table', () => {
  vi.mock('@tanstack/react-virtual', () => ({
    useVirtualizer: vi.fn(() => ({
      getVirtualItems: () =>
        mockVFiles.map((_, index) => ({
          index,
          size: 69,
          start: index * 69,
          key: index,
          measureElement: vi.fn(),
        })),
      getTotalSize: () => mockVFiles.length * 69,
    })),
  }));

  it('should render menu action correctly', async () => {
    const store = configureAppStore(initialState);
    store.dispatch = vi.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    const firstRowActionIcon = screen.getAllByTestId('search-table-menu')[0];
    userEvent.click(firstRowActionIcon);
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument();
      expect(screen.getByText('Edit Metadata')).toBeInTheDocument();
      expect(screen.getByText('Delete')).toBeInTheDocument();
    });
    userEvent.click(screen.getByText('Edit Metadata'));
    expect(store.dispatch).toHaveBeenCalledWith(
      toggleOpenEditDrawer(mockVFiles[0].id)
    );
  });

  it('shows grid view files when viewType is set to GRID', async () => {
    const gridState = cloneDeep({
      ...initialState,
      caseDetail: { ...initialState.caseDetail, viewType: ViewType.GRID },
    });
    const store = configureAppStore(gridState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      expect(screen.queryAllByTestId('file-card')[0]).toBeInTheDocument();
    });
  });

  it('shows list view files when viewType is set to LIST', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      expect(screen.queryByTestId('file-card')).not.toBeInTheDocument();
    });
  });

  it('should blur file cards when the blur switch is toggled and the viewType is GRID', () => {
    const gridState = cloneDeep({
      ...initialState,
      caseDetail: {
        ...initialState.caseDetail,
        viewType: ViewType.GRID,
        blur: false,
      },
    });
    const store = configureAppStore(gridState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    const blurSwitch = screen.getByTestId('blur-switch').querySelector('input');
    if (!blurSwitch) {
      throw new Error('Blur switch not found');
    }
    expect(blurSwitch.checked).toBe(false);
    userEvent.click(blurSwitch);
    expect(blurSwitch.checked).toBe(true);
  });

  it('should render CircularProgress when loading in GRID view', async () => {
    const gridLoadingState = cloneDeep({
      ...initialState,
      caseDetail: {
        ...initialState.caseDetail,
        viewType: ViewType.GRID,
        files: { ...initialState.caseDetail.files, status: 'loading' },
      },
    });
    const store = configureAppStore(gridLoadingState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      expect(
        screen.getByTestId('file-table__card-view-loading')
      ).toBeInTheDocument();
    });
  });

  it('should select a row when clicking checkbox', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    const checkbox = getCheckbox(`check-box__check-row-${mockVFiles[0].id}`);
    expect(checkbox).not.toBeChecked();
    userEvent.click(checkbox);
    await waitFor(() => {
      expect(checkbox).toBeChecked();
    });
  });

  it('should select multiple rows with Shift key', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    userEvent.click(getCheckbox(`check-box__check-row-${mockVFiles[0].id}`));
    userEvent.click(getCheckbox(`check-box__check-row-${mockVFiles[2].id}`), {
      shiftKey: true,
    });
    await waitFor(() => {
      expect(
        getCheckbox(`check-box__check-row-${mockVFiles[0].id}`)
      ).toBeChecked();
      expect(
        getCheckbox(`check-box__check-row-${mockVFiles[1].id}`)
      ).toBeChecked();
      expect(
        getCheckbox(`check-box__check-row-${mockVFiles[2].id}`)
      ).toBeChecked();
      expect(
        getCheckbox(`check-box__check-row-${mockVFiles[3].id}`)
      ).not.toBeChecked();
    });
  });

  it('should unselect a row when unchecking its checkbox in file table', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    const checkbox = getCheckbox(`check-box__check-row-${mockVFiles[0].id}`);
    userEvent.click(checkbox);
    await waitFor(() => expect(checkbox).toBeChecked());
    userEvent.click(checkbox);
    await waitFor(() => expect(checkbox).not.toBeChecked());
  });
});
