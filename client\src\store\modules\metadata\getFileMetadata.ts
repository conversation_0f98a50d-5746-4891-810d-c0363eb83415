import { G<PERSON><PERSON><PERSON> } from '@utils/helpers';
import { getSummary } from './getSummary';
import { Metadata } from '@shared-types/metadata';
import { FileStatus, RecordingMetadata, Task } from '@shared-types/types';
import { isEmpty, sortBy } from 'lodash';

export async function getFileMetadataByFolderId(
  folderId: string,
  offset: number,
  limit: number,
  evidenceTypeSchemaId: string,
  gql: GQLApi
) {
  const rawFileMetadata = await gql.getFileMetadataByFolderId(
    folderId,
    offset,
    limit
  );
  if (!rawFileMetadata) {
    throw new Error('no file metadata');
  }

  const filteredRecords = rawFileMetadata.folder.childTDOs.records.filter(
    (tdo) => !tdo.details.tags?.some((tag) => tag.key === 'toBeDeletedTime')
  );

  return mapFileMetadata(filteredRecords, evidenceTypeSchemaId, gql);
}

export async function getFileMetadata(
  fileIds: string[],
  evidenceTypeSchemaId: string,
  gql: GQLApi
) {
  const rawFileMetadata = await gql.getFileMetadata(fileIds);
  if (!rawFileMetadata) {
    throw new Error('no file metadata');
  }

  return mapFileMetadata(
    rawFileMetadata.temporalDataObjects.records,
    evidenceTypeSchemaId,
    gql
  );
}

const mapFileMetadata = async (
  records: RecordingMetadata[],
  evidenceTypeSchemaId: string,
  gql: GQLApi
) => {
  const userIds = [
    ...new Set(
      records.map((tdo) => tdo.createdBy).filter((id): id is string => !!id)
    ),
  ];
  const usersResponse =
    userIds.length > 0 ? await gql.getUsersInfo({ userIds }) : null;
  const usersList = usersResponse?.data.users?.records ?? [];

  const usersMapper = new Map(usersList.map((user) => [user.id, user]));

  return Promise.all(
    records.map(async (tdo) => {
      const user = tdo.createdBy ? usersMapper.get(tdo.createdBy) : undefined;
      const contentTemplate = tdo.assets.records.find(
        (asset) => asset.jsondata.schemaId === evidenceTypeSchemaId
      );
      const evidenceTypeJson: unknown = JSON.parse(
        contentTemplate?.transform ?? '{}'
      );
      if (typeof evidenceTypeJson !== 'object') {
        throw new Error('Invalid evidence type metadata');
      }
      const fileCase = tdo.folders.find(
        (folder) => folder.contentTemplates.length > 0
      )?.contentTemplates?.[0]?.sdo?.data;
      const summary = await getSummary(tdo.id, gql); // TODO: Optimize

      const fileMetadata: Metadata = {
        /* File Metadata */
        aiwareTdoId: tdo.id,
        description: tdo.description,
        uploadedDate: tdo.createdDateTime,
        modifiedDate: tdo.modifiedDateTime,
        fileSize: tdo.details.veritoneFile.size,
        fileFormat: tdo.name?.match(/\.([0-9a-z]+)(?:[\?#]|$)/i)?.[1] ?? '',
        mimeType: tdo.details.veritoneFile.mimetype,
        duration: tdo.primaryAsset?.jsondata?.mediaDuration ?? 0,
        fileName:
          tdo.details.veritoneFile.fileName ??
          tdo.details.veritoneFile.filename,
        veritoneFile: tdo.details.veritoneFile,
        caseId: fileCase?.caseId ?? '',
        caseName: fileCase?.caseName ?? '',
        aiCognitionEngineOutput: [],
        summary,
        sourceName: tdo.details.sourceName ?? '',
        creator: user ? `${user.firstName} ${user.lastName}` : undefined,

        /* Generic Investigate Metadata */
        sourceId: '',
        contentType: getContentType(tdo.details.veritoneFile.mimetype),
        fileStatus: isEmpty(tdo.tasks?.records)
          ? undefined
          : pickStatus(tdo.tasks!.records),

        /* Evidence Type Metadata */
        evidenceType: '',
        ...evidenceTypeJson,
      };
      return fileMetadata;
    })
  );
};

export const getContentType = (mimeType?: string) => {
  if (!mimeType) {
    return 'document';
  }
  if (mimeType.startsWith('video/')) {
    return 'video';
  }

  if (mimeType.startsWith('audio/')) {
    return 'audio';
  }

  if (mimeType.startsWith('image/')) {
    return 'image';
  }

  return 'document';
};

export function pickStatus(tasks: Task[]): FileStatus {
  const sortedTasks = sortBy(tasks, 'modifiedDateTime');
  const taskStatusByEngine = {} as Record<string, string>;
  for (const task of sortedTasks) {
    const engineId = task.engine?.id;
    if (engineId) {
      taskStatusByEngine[engineId] = task.status;
    }
  }
  const statuses = Object.values(taskStatusByEngine);
  if (
    statuses.some((s) => s === 'failed' || s === 'aborted' || s === 'cancelled')
  ) {
    return 'error';
  }
  if (statuses.some((s) => s !== 'complete')) {
    return 'pending';
  }
  return 'processed';
}
