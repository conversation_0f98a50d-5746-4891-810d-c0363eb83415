import './index.scss';
import FileTable from '@components/CaseManager/FileTable';
import { useDataDetailsPanel } from '@hooks';
import { I18nTranslate } from '@i18n';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useAppDispatch } from '@store/hooks';
import {
  selectCaseFiles,
  setCaseDetailFormValues,
} from '@store/modules/caseDetail/slice';
import {
  openInTrack,
  selectAllFolderIds,
  selectCaseData,
  selectCaseStatus,
  selectCases,
} from '@store/modules/caseManager/slice';
import {
  selectConfig,
  selectEvidenceTypeSchema,
} from '@store/modules/config/slice';
import {
  fetchMetadata,
  selectMetadataByFolderId,
} from '@store/modules/metadata/slice';
import { uploadFile } from '@utils/files';
import {
  updatePendingDeleteFileToLocalStorage,
  updatePendingMoveFileToLocalStorage,
} from '@utils/saveToLocalStorage';
import { debounce } from 'lodash';
import { useSnackbar } from 'notistack';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router';
import CaseDetails from '../CaseDetails';
import CaseTableWrapper from '../CaseDetailTableWrapper';
import { filterFormDefaultValues } from '@components/FilterPanel';
import { Location } from '@shared-types/types';

const DEBOUNCE_WAIT_MS = 350;
const debouncedSelectFile = debounce((callback: () => void) => {
  callback();
}, DEBOUNCE_WAIT_MS);

const CaseDetailsTable = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const intl = I18nTranslate.Intl();
  const cases = useSelector(selectCases);
  const files = useSelector(selectCaseFiles);
  const { redactUrl, registryIds } = useSelector(selectConfig);
  const [selected, setSelected] = useState('');
  const selectedCaseData = useSelector(selectCaseData);
  const selectedCaseStatus = useSelector(selectCaseStatus);
  const evidenceTypeSchemaId = useSelector(selectEvidenceTypeSchema).id;
  const metadataByFolderId = useSelector(selectMetadataByFolderId);
  const { selectedFolderId, tdoId } = useParams();
  const allFolderIds = useSelector(selectAllFolderIds);
  const [pendingMoveFileIds, setPendingMoveFileIds] = useState<
    { fileId: string; newFolderId: string; oldFolderId: string }[]
  >([]);
  const [pendingDeleteFileIds, setPendingDeleteFileIds] = useState<string[]>(
    []
  );
  const location: Location = useLocation();
  const fromPath = location?.state?.from;

  const { data: filesData } = files;
  const totalFiles = filesData.totalResults;
  useEffect(
    () => () => {
      dispatch(setCaseDetailFormValues(filterFormDefaultValues));
    },
    [dispatch]
  );

  useEffect(() => {
    if (selectedCaseStatus === 'failure') {
      if (fromPath === '/search') {
        enqueueSnackbar({
          message: intl.formatMessage({
            id: 'unableToLoadCaseFromSearch',
            defaultMessage:
              'Unable to load case. Redirecting back to search results.',
          }),
          variant: 'warning',
        });
        navigate(`/search`);
      } else {
        navigate(`/case-manager`);
      }
    }
  }, [selectedCaseData, selectedCaseStatus]);

  useEffect(() => {
    try {
      const validDeleteFileTdoIds = updatePendingDeleteFileToLocalStorage();
      setPendingDeleteFileIds(validDeleteFileTdoIds.map((item) => item.value));
    } catch (e) {
      console.error('Unable to update deleted file tdoIds to local storage', e);
    }

    try {
      const validMoveFileTdoIds = updatePendingMoveFileToLocalStorage();
      setPendingMoveFileIds(validMoveFileTdoIds.map((item) => item.value));
    } catch (e) {
      console.error('Unable to update moved file tdoIds to local storage', e);
    }
  }, [files]);

  useEffect(() => {
    if (
      selectedFolderId &&
      evidenceTypeSchemaId &&
      (metadataByFolderId?.[selectedFolderId] === undefined ||
        metadataByFolderId[selectedFolderId]?.status === 'idle')
    ) {
      dispatch(
        fetchMetadata({ folderId: selectedFolderId, evidenceTypeSchemaId })
      );
    }
  }, [selectedFolderId, evidenceTypeSchemaId, metadataByFolderId, dispatch]);

  const handleSelect = (selectedId: string) => {
    setSelected(selectedId);

    const selectedCase = cases.data.results.find(
      (result) => result.id === selectedId
    );
    if (selectedCase) {
      setSelectedFolderId(selectedCase.folderId);
    }

    if (selectedFolderId && tdoId) {
      debouncedSelectFile(() => {
        // TODO: Handle navigating to other data details without the need to navigate to case details first
        navigate(`/case-manager/${selectedFolderId}`);
        setTimeout(() => {
          navigate(
            `/case-manager/${selectedFolderId}/data-details/${selectedId}`
          );
        }, 100);
      });
    }
  };

  const handleDoubleClick = (tdoId: string) => {
    navigate(`/case-manager/${selectedFolderId}/data-details/${tdoId}`);
  };

  const handleSendToRedact = (tdoId: string) => {
    window.open(
      `${redactUrl}${selectedFolderId ? `/case/${selectedFolderId}` : ''}/files/${tdoId}`,
      '_blank'
    );
  };

  const handleSendToTrack = (tdoId: string) => {
    if (selectedFolderId) {
      dispatch(
        openInTrack({
          tdoId,
          folderId: selectedFolderId,
          folderName:
            selectedCaseData.caseId ??
            selectedCaseData.caseName ??
            'Imported Case',
          caseDate: selectedCaseData.caseDate ?? '',
          description: selectedCaseData.description ?? '',
        })
      );
    }
  };

  const handleBackClick = () => {
    navigate('/case-manager');
  };

  const handleUploadFile = () => {
    if (!allFolderIds || allFolderIds.length === 0) {
      enqueueSnackbar(intl.formatMessage({ id: 'noCasesPleaseCreateACase' }), {
        variant: 'error',
      });
      return;
    }

    uploadFile({
      selectedFolderId,
      selectedCaseId: selectedCaseData?.caseId,
      registryId: registryIds?.caseRegistryId,
      intl,
      orgFolderIds: allFolderIds,
    });
  };

  useDataDetailsPanel({
    tdoId,
    navigationPath: `/case-manager/${selectedFolderId}`,
  });

  const setSelectedFolderId = (folderId: string) => {
    navigate(`/case-manager/${folderId}`);
  };

  return (
    <div className="case-details-table">
      <div className="case-details-table__header">
        <div
          style={{ cursor: 'pointer' }}
          className="case-details-table__header__title"
          onClick={handleBackClick}
        >
          <ArrowBackIcon sx={{ width: 14, height: 14 }} />
          {I18nTranslate.TranslateMessage('backToCaseManager')}
        </div>
        <div className="case-details-table__header__case-detail">
          {I18nTranslate.TranslateMessage('caseDetail')}
        </div>
        <div className="case-details-table__header__case-count">
          {totalFiles}
        </div>
      </div>
      <div className="case-details-table__content">
        <CaseTableWrapper
          caseSubtitle={selectedCaseData?.caseId || ''}
          data-testid="case-details-wrapper"
        >
          <FileTable
            selected={selected}
            handleSelect={handleSelect}
            handleDoubleClick={(tdoId) => handleDoubleClick(tdoId)}
            handleSendToRedact={
              redactUrl ? (tdoId) => handleSendToRedact(tdoId) : undefined
            }
            handleSendToTrack={(tdoId) => handleSendToTrack(tdoId)}
            handleUploadFile={handleUploadFile}
            setSelectedFolderId={setSelectedFolderId}
            selectedFolderId={selectedFolderId ?? ''}
            setSelected={setSelected}
            pendingDeleteIds={pendingDeleteFileIds}
            setPendingDeleteIds={setPendingDeleteFileIds}
            pendingMoveFileIds={pendingMoveFileIds}
            classname="file__table"
          />
        </CaseTableWrapper>
        <CaseDetails folderId={selectedFolderId} />
      </div>
    </div>
  );
};

export default CaseDetailsTable;
