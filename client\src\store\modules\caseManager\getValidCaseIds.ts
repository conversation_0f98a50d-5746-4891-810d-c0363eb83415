import { GQLApi } from '@utils/helpers';

export async function getValidCaseIds({
  limit,
  offset,
  gql,
}: {
  limit: number;
  offset: number;
  gql: GQLApi;
}) {
  let validFolders: string[] = [];
  let hasMoreData = true;
  let currentOffset = offset;

  do {
    const allFolders = await gql.rootFolders('cms', limit, currentOffset);
    const rootFolder = allFolders.find((folder) => folder.ownerId === null);

    validFolders = validFolders.concat(
      rootFolder?.childFolders?.records.map((folder) => folder.id) || []
    );

    hasMoreData = rootFolder?.childFolders?.records.length === limit;
    currentOffset += limit;
  } while (hasMoreData);

  if (!validFolders.length) {
    throw new Error('there are no valid cases');
  }

  return validFolders;
}
