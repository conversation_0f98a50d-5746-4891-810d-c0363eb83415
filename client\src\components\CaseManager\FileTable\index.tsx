import './index.scss';
import {
  Blur,
  DeleteX,
  EditAttributes,
  FileNullState,
  View,
} from '@assets/icons';
import EmptyState from '@components/CaseManager/EmptyState';
import Dialog from '@components/Dialog';
import FileCard from '@components/FileCard';
import MoveFileDialog from '@components/MoveFileDialog';
import { BlurSwitch } from '@components/Search/Switch';
import Table, { Action, Column, DataMap } from '@components/Table';
import { I18nTranslate } from '@i18n';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import WorkOutlineOutlinedIcon from '@mui/icons-material/WorkOutlineOutlined';
import ExitToAppOutlinedIcon from '@mui/icons-material/ExitToAppOutlined';
import { Box, Checkbox, CircularProgress, Tooltip } from '@mui/material';
import { VFile } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import { handleFileCardArrowKeys } from '@utils/helpers/handleFileCardArrowKeys';

import {
  getFiles,
  pollFilesForCase,
  resetQueryParams,
  searchFolders,
  selectBlur,
  selectCaseFiles,
  selectDestinationCase,
  selectLimit,
  selectOffset,
  selectPendingFiles,
  selectSelectedFiles,
  selectSortBy,
  selectSortDirection,
  selectViewType,
  setDestinationCase,
  setLimit,
  setOffset,
  setSelectedFileId,
  setSelectedFiles,
  setSortBy,
  setSortDirection,
  syncPendingFiles,
  toggleBlur,
  toggleMoveFileDialog,
} from '@store/modules/caseDetail/slice';
import {
  deleteFile,
  selectCaseData,
  selectRootFolderId,
} from '@store/modules/caseManager/slice';
import {
  editMetadata,
  toggleOpenEditDrawer,
  selectMetadataByFolderId,
} from '@store/modules/metadata/slice';
import { getThumbnailUrl } from '@utils/getThumbnails';
import { dispatchCustomEvent, voidWrapper } from '@utils/helpers';
import { ViewType } from '@utils/local-storage';
import { savePendingDeleteFileToLocalStorage } from '@utils/saveToLocalStorage';
import { map } from 'p-iteration';
import { ChangeEvent, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import { ArrayOrSingle } from 'ts-essentials';
import FileStatus from './FileStatus';

export enum OrderBy {
  FILE_TYPE = 'fileType',
  FILE_NAME = 'veritone-file.filename',
  UPLOAD_DATE = 'createdTime',
}

interface Props {
  selected: string;
  handleSelect: (selectedId: string) => void;
  handleDoubleClick: (tdoId: string) => void;
  handleSendToRedact?: (tdoId: string) => void;
  handleSendToTrack?: (tdoId: string) => void;
  handleUploadFile: () => void;
  setSelectedFolderId: (id: string) => void;
  selectedFolderId: string;
  setSelected: (id: string) => void;
  pendingDeleteIds: string[];
  setPendingDeleteIds: (ids: string[]) => void;
  pendingMoveFileIds: {
    fileId: string;
    newFolderId: string;
    oldFolderId: string;
  }[];
  classname?: string;
}

const TABLE_MIN_WIDTH = '1031px';

const FileTable = ({
  selected,
  handleSelect,
  handleDoubleClick,
  handleSendToRedact,
  handleSendToTrack,
  handleUploadFile,
  setSelectedFolderId,
  selectedFolderId,
  setSelected,
  pendingDeleteIds,
  setPendingDeleteIds,
  pendingMoveFileIds,
  classname,
}: Props) => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const { data: filesData, status } = useSelector(selectCaseFiles);
  const searchResultsFiles: VFile[] = filesData.results;
  const totalFiles = filesData.totalResults;

  const allMetadata = useSelector(selectMetadataByFolderId);
  const folderId = selectedFolderId;
  const updatedSearchResultsFiles = useMemo(() => {
    if (folderId && allMetadata[folderId]?.data) {
      const lookupFilesByFolderId = allMetadata[folderId].data;
      return searchResultsFiles.map((file) => {
        const detailFromFileMetadata = lookupFilesByFolderId[file.id];
        if (detailFromFileMetadata) {
          return {
            ...file,
            sourceName: detailFromFileMetadata.sourceName,
            evidenceType: detailFromFileMetadata.evidenceType,
            fileStatus: detailFromFileMetadata.fileStatus,
          };
        }
        return file;
      });
    }
    return searchResultsFiles;
  }, [folderId, allMetadata, searchResultsFiles]);

  const pendingFiles = useSelector(selectPendingFiles);
  const destinationCase = useSelector(selectDestinationCase);
  const [lastSelected, setLastSelected] = useState<number | null>(null);

  const offset = useSelector(selectOffset);
  const limit = useSelector(selectLimit);
  const sortBy = useSelector(selectSortBy);
  const sortDirection = useSelector(selectSortDirection);
  const selectedFiles = useSelector(selectSelectedFiles);
  const blurred = useSelector(selectBlur);
  const viewType = useSelector(selectViewType);
  const selectedCase = useSelector(selectCaseData);

  const selectedFilesSet = new Set(selectedFiles);
  const rootFolderId = useSelector(selectRootFolderId);

  const currentPendingFiles =
    pendingFiles[selectedFolderId]?.map((item) => item.value) || [];

  const fileMap = useMemo(
    () =>
      updatedSearchResultsFiles.reduce((acc: DataMap<VFile>, item, index) => {
        acc[item.id] = { index, item };
        return acc;
      }, {}),
    [updatedSearchResultsFiles]
  );
  useEffect(
    () => () => {
      dispatch(setSelectedFiles([]));
      dispatch(resetQueryParams());
    },
    [dispatch]
  );

  const isCheckAll = useMemo(
    () =>
      updatedSearchResultsFiles.length > 0 &&
      updatedSearchResultsFiles.every(({ id }) => selectedFilesSet.has(id)),
    [updatedSearchResultsFiles, selectedFilesSet]
  );
  const isFileLoading = status === 'loading';

  const [
    openSelectedFilesDeletionConfirmationDialog,
    setOpenSelectedFilesDeletionConfirmationDialog,
  ] = useState(false);

  const updateSelectedFiles = (rowIds: string[]) => {
    dispatch(setSelectedFiles(rowIds));
  };

  const [
    openFilesDeletionConfirmationDialog,
    setOpenFilesDeletionConfirmationDialog,
  ] = useState(false);
  const pollPromiseRef = useRef<DispatchPromise>(null);

  useEffect(() => {
    dispatchCustomEvent('enable-bulk-actions', {
      isEnabled: selectedFiles.length > 0,
    });
  }, [selectedFiles]);

  useEffect(() => {
    dispatch(syncPendingFiles({}));

    if (selectedFolderId && rootFolderId) {
      dispatch(getFiles({ folderId: selectedFolderId }));

      pollPromiseRef.current?.abort();
      pollPromiseRef.current = dispatch(pollFilesForCase(selectedFolderId));
    }

    return () => {
      pollPromiseRef.current?.abort();
    };
  }, [
    selectedFolderId,
    rootFolderId,
    offset,
    limit,
    sortBy,
    sortDirection,
    dispatch,
  ]);
  const handleCheckAll = () => {
    const rowIds = updatedSearchResultsFiles.map(({ id }) => id);
    let newSelectedFiles: string[] = [];

    if (!isCheckAll) {
      newSelectedFiles = selectedFiles.concat(
        rowIds.filter((id) => !selectedFilesSet.has(id))
      );
    } else {
      const rowIdSet = new Set(rowIds);
      newSelectedFiles = selectedFiles.filter((id) => !rowIdSet.has(id));
    }

    updateSelectedFiles(newSelectedFiles);
    setLastSelected(null);
  };

  const handleSlice = (start: number, end: number) =>
    selectedFiles.concat(
      updatedSearchResultsFiles
        .slice(start, end + 1)
        .map(({ id }) => id)
        .filter((id) => !selectedFilesSet.has(id))
    );

  const handleHoldShift = (id: string, lastIndex: number) => {
    const index = fileMap[id].index;
    return lastIndex > index
      ? handleSlice(index, lastIndex)
      : handleSlice(lastIndex, index);
  };

  const handleChangeCheckbox = (
    id: string,
    checked: boolean,
    e: ChangeEvent<HTMLInputElement>
  ) => {
    if (!fileMap[id]) {
      return;
    }

    const nativeEvent = e.nativeEvent;
    let newSelectedFiles: string[] = [];
    if (checked) {
      if (
        lastSelected !== null &&
        nativeEvent instanceof MouseEvent &&
        nativeEvent.shiftKey
      ) {
        newSelectedFiles = handleHoldShift(id, lastSelected);
      } else {
        newSelectedFiles = selectedFiles.concat([id]);
        const index = fileMap[id].index;
        setLastSelected(index);
      }
    } else {
      newSelectedFiles = selectedFiles.filter(
        (selectedId) => selectedId !== id
      );
      setLastSelected(null);
    }

    updateSelectedFiles(newSelectedFiles);
  };

  const handleCtrlRowClick = (id: string) => {
    if (!fileMap[id]) {
      return;
    }

    let newSelectedFiles: string[] = [];

    if (selectedFilesSet.has(id)) {
      newSelectedFiles = selectedFiles.filter(
        (selectedId) => selectedId !== id
      );
      setLastSelected(null);
    } else {
      newSelectedFiles = selectedFiles.concat([id]);
      setLastSelected(fileMap[id].index);
    }
    updateSelectedFiles(newSelectedFiles);
  };

  const handleShiftRowClick = (id: string) => {
    if (lastSelected !== null) {
      const newSelecteFiles = handleHoldShift(id, lastSelected);
      updateSelectedFiles(newSelecteFiles);
    }
  };

  const renderCell = (
    value: ArrayOrSingle<string> | undefined,
    isToolTip?: boolean
  ) => {
    const cell = (
      <div className="file-table__table-cell">
        <span>{value || intl.formatMessage({ id: 'defaultEmpty' })}</span>
      </div>
    );

    if (isToolTip) {
      return (
        <Tooltip
          title={value}
          placement="bottom-start"
          disableHoverListener={!value}
        >
          {cell}
        </Tooltip>
      );
    } else {
      return cell;
    }
  };

  const fileColumns: Column<VFile>[] = [
    {
      header: updatedSearchResultsFiles.length ? (
        <Checkbox
          data-testid="check-box__check-all"
          indeterminate={
            !isCheckAll &&
            updatedSearchResultsFiles.some((row) =>
              selectedFilesSet.has(row.id)
            )
          }
          checked={isCheckAll}
          onChange={(_e) => handleCheckAll()}
        />
      ) : null,
      render: ({ rowId }) =>
        rowId && (
          <Checkbox
            data-testid={`check-box__check-row-${rowId}`}
            checked={selectedFilesSet.has(rowId)}
            onChange={(e, checked) => handleChangeCheckbox(rowId, checked, e)}
            onClick={(e) => e.stopPropagation()}
            onDoubleClick={(e) => e.stopPropagation()}
          />
        ),
      width: '56px',
    },
    {
      field: 'fileName',
      header: intl.formatMessage({ id: 'fileName' }),
      render: ({ value }) =>
        renderCell(typeof value === 'number' ? String(value) : value, true),
      isSortable: true,
      width: '20%',
    },
    {
      field: 'id',
      header: intl.formatMessage({ id: 'fileId' }),
      render: ({ value }) =>
        renderCell(typeof value === 'number' ? String(value) : value, true),
      isSortable: true,
      width: '20%',
    },
    {
      field: 'sourceName',
      header: intl.formatMessage({ id: 'sourceName' }),
      render: ({ value }) =>
        renderCell(typeof value === 'number' ? String(value) : value, true),
      isSortable: true,
      width: '20%',
    },
    {
      field: 'evidenceType',
      header: intl.formatMessage({ id: 'evidenceType' }),
      render: ({ value }) =>
        renderCell(typeof value === 'number' ? String(value) : value, true),
      isSortable: true,
      width: '20%',
    },
    {
      field: 'fileStatus',
      header: intl.formatMessage({ id: 'status' }),
      render: ({ value }) => {
        const statusText = typeof value === 'string' && value ? value : 'N/A';
        return (
          <div className="file-table__table-cell">
            <FileStatus status={statusText} />
          </div>
        );
      },
      width: '12%',
    },
  ];

  const handleOpenMoveFile = (rowId: string) => {
    handleSelect(rowId);
    dispatch(setSelectedFileId(rowId));
    dispatch(toggleMoveFileDialog());
    dispatch(
      searchFolders({
        offset: 0,
        limit: 30,
      })
    );
  };

  const handleDispatchCurrentFile = (fileId: string) => {
    dispatch(setSelectedFileId(fileId));
  };

  const handleOpenEditMetadataDrawer = (rowId: string) => {
    handleSelect(rowId);
    dispatch(editMetadata(rowId));
    dispatch(toggleOpenEditDrawer(rowId));
  };

  const handleDelete = (rowId: string) => {
    setSelected(rowId);
    setOpenFilesDeletionConfirmationDialog(true);
  };

  const handleDeletionCancel = () => {
    setOpenFilesDeletionConfirmationDialog(false);
  };

  const handleFileDeletionConfirm = () => {
    const deleteId = selected;
    dispatch(deleteFile({ tdoId: deleteId, folderId: selectedFolderId }));

    try {
      const validCaseSdoIds = savePendingDeleteFileToLocalStorage([deleteId]);
      setPendingDeleteIds(validCaseSdoIds.map((item) => item.value));
    } catch {
      console.error('unable to save deleted file to local storage');
    }
    setSelected('');
    dispatch(getFiles({ folderId: selectedFolderId }));
    setOpenFilesDeletionConfirmationDialog(false);
  };

  const fileActions: Action[] = [
    {
      action: intl.formatMessage({ id: 'viewFile' }),
      icon: <View />,
      onClick: handleDoubleClick,
    },
    {
      action: intl.formatMessage({ id: 'editMetadata' }),
      icon: <EditAttributes />,
      onClick: handleOpenEditMetadataDrawer,
    },
    {
      action: intl.formatMessage({ id: 'move' }),
      icon: <WorkOutlineOutlinedIcon />,
      onClick: handleOpenMoveFile,
      isDivider: true,
    },
    ...(handleSendToRedact
      ? [
          {
            action: intl.formatMessage({ id: 'sendToRedact' }),
            icon: <ExitToAppOutlinedIcon />,
            onClick: handleSendToRedact,
            isDisabled: (rowId: string) => {
              const file = updatedSearchResultsFiles.find(
                (file) => file.id === rowId
              );
              const type = file?.fileType?.split('/')[0];
              return type !== 'video' && type !== 'audio';
            },
          },
        ]
      : []),
    ...(handleSendToTrack
      ? [
          {
            action: intl.formatMessage({ id: 'sendToTrack' }),
            icon: <ExitToAppOutlinedIcon />,
            onClick: handleSendToTrack,
            isDivider: true,
            isDisabled: (rowId: string) => {
              const file = updatedSearchResultsFiles.find(
                (file) => file.id === rowId
              );
              const type = file?.fileType?.split('/')[0];
              return type !== 'video';
            },
          },
        ]
      : []),
    {
      action: intl.formatMessage({ id: 'delete' }),
      icon: <DeleteOutlineIcon />,
      onClick: handleDelete,
    },
  ];

  const renderEmptyState = () => (
    <EmptyState
      imageSrc={<FileNullState className="empty-state-icon" />}
      title={I18nTranslate.TranslateMessage('noFilesFound')}
      description={I18nTranslate.TranslateMessage('noFilesFoundDescription', {
        br: <br key="line-break" />,
      })}
      buttonText={I18nTranslate.TranslateMessage('addFiles')}
      onClick={handleUploadFile}
    />
  );

  const isValidOrderBy = (value: string): value is OrderBy =>
    Object.values(OrderBy).includes(value as OrderBy);

  const handleSort = (key: string) => {
    if (sortBy === key) {
      dispatch(setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc'));
      setSelected('');
    } else if (isValidOrderBy(key)) {
      dispatch(setSortBy(key));
      setSelected('');
    }
  };

  const onPageChange = (
    _event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {
    dispatch(setOffset(newPage * limit));
    setLastSelected(null);
    setSelected('');
  };

  const onRowsPerPageChange = (event: ChangeEvent<HTMLInputElement>) => {
    const oldPage = Math.floor(offset / limit);
    const newLimit = Number(event.target.value);
    let newOffset = oldPage * newLimit;

    // Ensure newOffset does not exceed total results
    if (newOffset >= totalFiles) {
      newOffset = Math.floor(totalFiles / newLimit) * newLimit;
    }

    dispatch(setOffset(newOffset));
    dispatch(setLimit(newLimit));

    setLastSelected(null);
    setSelected('');
  };

  useEffect(() => {
    if (destinationCase.data?.folderId) {
      // Navigate to the destination case
      navigate(`/case-manager/${destinationCase.data.folderId}`);
      dispatch(getFiles({ folderId: destinationCase.data.folderId }));
      // This causes the destination case to be loaded after move!
      // Also re-fetch destination case in Case Detail panel
      setSelectedFolderId(destinationCase.data.folderId);

      dispatch(setDestinationCase(undefined));
    }
  }, [destinationCase]);

  const handleDeleteAllSelectedFiles = () => {
    if (selectedFiles.length === 0) {
      return;
    }

    setOpenSelectedFilesDeletionConfirmationDialog(true);
  };

  const handleMoveAllSelectedFiles = () => {
    if (selectedFiles.length === 0) {
      return;
    }

    handleSelect(selectedFiles[0]);

    dispatch(
      searchFolders({
        offset: 0,
        limit: 30,
      })
    );
    dispatch(
      toggleMoveFileDialog({
        isBulk: true,
        moveFileDialogBulkFileIds: selectedFiles,
      })
    );
  };

  const handleCheckedDeletionCancel = () => {
    setOpenSelectedFilesDeletionConfirmationDialog(false);
  };

  const handleCheckedFileDeletionConfirm = async () => {
    // TODO: consider changing deleteFile to accept an array of IDs
    await map(selectedFiles, (deleteId) => {
      dispatch(deleteFile({ tdoId: deleteId, folderId: selectedFolderId }));
    });
    try {
      const deletedTdoIds = savePendingDeleteFileToLocalStorage(selectedFiles);
      setPendingDeleteIds(deletedTdoIds.map((item) => item.value));
    } catch {
      console.error('unable to save deleted file to local storage');
    }
    dispatch(setSelectedFiles([]));
    dispatch(getFiles({ folderId: selectedFolderId }));
    setOpenSelectedFilesDeletionConfirmationDialog(false);
  };

  const handleKeyUp = (e: KeyboardEvent) =>
    handleFileCardArrowKeys(e, updatedSearchResultsFiles);

  useEffect(() => {
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [updatedSearchResultsFiles]);

  useEffect(() => {
    window.addEventListener(
      'delete-all-selected-files',
      handleDeleteAllSelectedFiles
    );
    window.addEventListener(
      'move-all-selected-files',
      handleMoveAllSelectedFiles
    );

    return () => {
      window.removeEventListener(
        'delete-all-selected-files',
        handleDeleteAllSelectedFiles
      );
      window.removeEventListener(
        'move-all-selected-files',
        handleMoveAllSelectedFiles
      );
    };
  });

  return (
    <>
      {viewType === ViewType.GRID && (
        <Box className="file-table__grid-view-header">
          <Checkbox
            onClick={(e) => e.stopPropagation()}
            onChange={handleCheckAll}
            checked={isCheckAll}
          />
          <div className="file-table__blur-images-wrapper">
            <span className="file-table__blur-label">
              {intl.formatMessage({ id: 'blurImages' })}
            </span>
            <BlurSwitch
              checked={blurred}
              icon={<Blur />}
              checkedIcon={<Blur />}
              onChange={() => dispatch(toggleBlur())}
              data-testid="blur-switch"
            />
          </div>
        </Box>
      )}
      {viewType === ViewType.GRID && (
        <Box
          gap={3}
          flex={1}
          display="flex"
          flexWrap="wrap"
          justifyContent="center"
          className="file-table__card-view-content table-loader"
        >
          {isFileLoading ? (
            <CircularProgress data-testid="file-table__card-view-loading" />
          ) : updatedSearchResultsFiles.length ? (
            updatedSearchResultsFiles.map((row, index) => {
              const thumbnailUrl = getThumbnailUrl(
                row.fileType,
                row.programLiveImage || ''
              );
              const type = row?.fileType?.split('/')?.[0];

              return (
                <FileCard
                  index={index}
                  fileId={row.id}
                  key={row.id}
                  fileName={row.fileName}
                  fileType={row.fileType}
                  dateUploaded={intl.formatDate(row.createdTime)}
                  caseId={selectedCase?.caseId}
                  folderId={selectedFolderId}
                  blurred={blurred}
                  fileDuration={row.duration ?? -1}
                  isChecked={selectedFilesSet.has(row.id)}
                  onCheck={handleChangeCheckbox}
                  onMove={() => handleOpenMoveFile(row.id)}
                  onOpenEditMetadataDrawer={handleOpenEditMetadataDrawer}
                  onViewFile={() => handleDoubleClick(row.id)}
                  onSendToRedact={
                    handleSendToRedact && (type === 'video' || type === 'audio')
                      ? () => handleSendToRedact(row.id)
                      : undefined
                  }
                  onSendToTrack={
                    handleSendToTrack && type === 'video'
                      ? () => handleSendToTrack(row.id)
                      : undefined
                  }
                  onDelete={() => handleDelete(row.id)}
                  thumbnailUrl={thumbnailUrl}
                  isDefaultThumbnail={thumbnailUrl === row.programLiveImage}
                  isPending={
                    pendingDeleteIds.includes(row.id) ||
                    pendingMoveFileIds
                      .map((files) => files.fileId)
                      .includes(row.id)
                  }
                />
              );
            })
          ) : (
            <EmptyState
              imageSrc={<FileNullState className="empty-state-icon" />}
              title={I18nTranslate.TranslateMessage('noFilesFound')}
              description={I18nTranslate.TranslateMessage(
                'noFilesFoundDescription',
                {
                  br: <br key="line-break" />,
                }
              )}
              buttonText={I18nTranslate.TranslateMessage('addFiles')}
              onClick={handleUploadFile}
            />
          )}
        </Box>
      )}
      {viewType === ViewType.LIST && (
        <Table<VFile>
          data={updatedSearchResultsFiles}
          dataMap={fileMap}
          pendingDataCreate={currentPendingFiles}
          columns={fileColumns}
          row={{
            selected,
            handleSelect,
            handleDoubleClick,
            handleCtrlRowClick,
            handleShiftRowClick,
            pendingDeleteIds,
            pendingMoveFileIds,
          }}
          sort={{
            orderBy: sortBy,
            direction: sortDirection,
            handleSort,
          }}
          pagination={{
            page: Math.floor(offset / limit),
            count: totalFiles,
            rowsPerPage: limit,
            rowsPerPageOptions: [50, 100, 200, 300],
            onPageChange,
            onRowsPerPageChange,
          }}
          styles={{
            classname,
            isLoading: isFileLoading,
            emptyState: renderEmptyState(),
            isFixedTableLayout: true,
            noneCopyCell: true,
            fixedTableMinWidth: TABLE_MIN_WIDTH,
          }}
          actions={fileActions}
          extraProps={{
            dispatchCurrentFile: handleDispatchCurrentFile,
            pendingDeleteMessage: intl.formatMessage({
              id: 'filePendingDelete',
            }),
          }}
        />
      )}
      <MoveFileDialog />
      <Dialog
        open={openFilesDeletionConfirmationDialog}
        title={intl.formatMessage({ id: 'areYouSure' })}
        onClose={handleDeletionCancel}
        onConfirm={handleFileDeletionConfirm}
        confirmText={intl.formatMessage({ id: 'yesDeleteFile' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
        isDelete
        useTypeConfirmation
        disableConfirm={false}
      >
        <div>
          {I18nTranslate.TranslateMessage('deleteFilesConfirmationMsg')}
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('theFileWillBeDeleted')}
          </span>
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('everyOneInOrgWillLoseAccess')}
          </span>
        </div>
      </Dialog>
      <Dialog
        open={openSelectedFilesDeletionConfirmationDialog}
        title={intl.formatMessage({ id: 'areYouSure' })}
        onClose={handleCheckedDeletionCancel}
        onConfirm={voidWrapper(handleCheckedFileDeletionConfirm)}
        confirmText={intl.formatMessage({ id: 'yesDeleteFiles' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
        isDelete
        useTypeConfirmation
        disableConfirm={false}
        data-testid="delete-selected-files-dialog"
      >
        <div>
          {I18nTranslate.TranslateMessage('deleteFilesConfirmationMsg')}
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('allSelectedFilesWillBeDeleted')}
          </span>
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('everyOneInOrgWillLoseAccess')}
          </span>
        </div>
      </Dialog>
    </>
  );
};

export default FileTable;
