import { configureAppStore } from '@store/index';
import { screen, waitFor } from '@testing-library/dom';
import { Provider } from 'react-redux';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { render } from '../../../test/render';
import { GQLApi } from '@utils/helpers';
import PermissionsPanel from '.';
import {
  initialState as settingsInitialState,
  SettingsSliceState,
} from '@store/modules/settings/slice.ts';
import {
  CaseManagerSliceState,
  initialState as caseManagerInitialState,
} from '@store/modules/caseManager/slice.ts';
import {
  permsissionedCaseFolderData,
  mockOrgPermissionSets,
  allOrgAuthGroups,
  allOrgUsers,
  permsissionedCaseFolderDataWithoutTucker,
  permsissionedCaseFolderDataWithBenHaAdded,
} from './MockTestData/PermissionPanelTestData';
import userEvent from '@testing-library/user-event';
import {
  ACLR<PERSON>ordType,
  AuthGroup,
  AuthPermissionSet,
  User,
} from '@utils/olp/types.ts';
import { cloneDeep } from 'lodash';
import { fireEvent } from '@testing-library/react';

/* cSpell:disable */
const initialStateForMock: {
  settings: SettingsSliceState;
  caseManager: CaseManagerSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  settings: settingsInitialState,
  caseManager: caseManagerInitialState,
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    dataRegistry: {
      caseRegistry: {
        name: 'caseRegistryName123',
        id: 'caseRegistryId123',
      },
      statusRegistry: {
        name: 'statusRegistryName123',
        id: 'statusRegistryId123',
      },
      tagRegistry: {
        name: 'tagRegistryName123',
        id: 'tagRegistryId123',
      },
      evidenceTypeRegistry: {
        name: 'evidenceTypeRegistryName123',
        id: 'evidenceTypeRegistryId123',
      },
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

describe('Create Case', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    //  Clone these so we are working with fresh copy of data each test.
    const permissionedCaseDataCloned = cloneDeep(permsissionedCaseFolderData);
    const allOrgUsersCloned = cloneDeep(allOrgUsers);
    const allOrgGroupsCloned = cloneDeep(allOrgAuthGroups);

    vi.spyOn(GQLApi.prototype, 'getACLForResources').mockImplementation(() =>
      Promise.resolve(
        permissionedCaseDataCloned as { records: ACLRecordType[] }
      )
    );
    vi.spyOn(GQLApi.prototype, 'getAuthPermissionSets').mockImplementation(() =>
      Promise.resolve(mockOrgPermissionSets as unknown as AuthPermissionSet[])
    );
    vi.spyOn(GQLApi.prototype, 'getAllOrgUsers').mockImplementation(() =>
      Promise.resolve(allOrgUsersCloned as unknown as { records: User[] })
    );
    vi.spyOn(GQLApi.prototype, 'getAllAuthGroups').mockImplementation(() =>
      Promise.resolve(allOrgGroupsCloned as unknown as { records: AuthGroup[] })
    );
  });

  it('renders PermissionsPanel component', () => {
    const store = configureAppStore(initialStateForMock);

    // Mock a close function.
    const mockOnClose = vi.fn();

    render(
      <Provider store={store}>
        <PermissionsPanel onClose={mockOnClose} />
      </Provider>
    );

    expect(screen.getByText('Permissions')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Grant access for this case to specific people or groups.'
      )
    ).toBeInTheDocument();
  });

  it('fetches and displays people permissioned for the case', async () => {
    const store = configureAppStore({
      ...initialStateForMock,
      caseManager: {
        ...initialStateForMock.caseManager,
        editingCaseFolderId: 'caseFolderId123',
      },
    });

    const mockOnClose = vi.fn();

    render(
      <Provider store={store}>
        <PermissionsPanel onClose={mockOnClose} />
      </Provider>
    );

    await waitFor(() => {
      // Jordan and Tucker should be in the list as they are permission-ed
      expect(screen.getByText('Jordan Ghidossi')).toBeInTheDocument();
      expect(screen.getByText('Tucker Roth')).toBeInTheDocument();
    });

    const editorElements = screen.getAllByText('Editor');
    expect(editorElements.length).toBeGreaterThan(0);

    // Phong Vu should not be in the list as they are not permission-ed
    expect(screen.queryByText('Phong Vu')).not.toBeInTheDocument();
  });

  it('displays permissioned groups after user clicks Groups tab', async () => {
    const store = configureAppStore({
      ...initialStateForMock,
      caseManager: {
        ...initialStateForMock.caseManager,
        editingCaseFolderId: 'caseFolderId123',
      },
    });

    const mockOnClose = vi.fn();

    render(
      <Provider store={store}>
        <PermissionsPanel onClose={mockOnClose} />
      </Provider>
    );

    await waitFor(() => {
      // Jordan and Tucker should be in the list as they are permission-ed
      expect(screen.getByText('Jordan Ghidossi')).toBeInTheDocument();
      expect(screen.getByText('Tucker Roth')).toBeInTheDocument();
    });

    const editorElements = screen.getAllByText('Editor');
    expect(editorElements.length).toBeGreaterThan(0);

    // Phong Vu should not be in the list as they are not permission-ed
    expect(screen.queryByText('Phong Vu')).not.toBeInTheDocument();

    const peoplesTab = screen.getByTestId('peoples-tab');
    expect(peoplesTab).toBeEnabled();

    const groupsTab = screen.getByTestId('groups-tab');
    userEvent.click(groupsTab);

    await waitFor(() => {
      expect(groupsTab).toBeEnabled();
    });

    // The person Tucker Roth is no longer showing, because we are in the Groups tab
    expect(screen.queryByText('Tucker Roth')).not.toBeInTheDocument();

    // The group "Investigate Editors" should be showing as it is permission-ed
    expect(screen.getByText('Investigate Editor')).toBeInTheDocument();
    // It has 2 members
    const investigateEditorsMembers = screen.getAllByText('2 members');
    expect(investigateEditorsMembers.length).toBeGreaterThan(0);
  });

  it('A person can be removed from permissioned users', async () => {
    const store = configureAppStore({
      ...initialStateForMock,
      caseManager: {
        ...initialStateForMock.caseManager,
        editingCaseFolderId: 'caseFolderId123',
      },
    });

    const mockRemoveResources = vi
      .spyOn(GQLApi.prototype, 'removeACEsFromResources')
      .mockImplementation(() =>
        Promise.resolve(
          permsissionedCaseFolderDataWithoutTucker as {
            records: ACLRecordType[];
          }
        )
      );

    const mockOnClose = vi.fn();

    render(
      <Provider store={store}>
        <PermissionsPanel onClose={mockOnClose} />
      </Provider>
    );

    await waitFor(() => {
      // Jordan and Tucker should be in the list as they are permission-ed
      expect(screen.getByText('Jordan Ghidossi')).toBeInTheDocument();
      expect(screen.getByText('Tucker Roth')).toBeInTheDocument();
    });

    const tuckerRemoveButton = screen.getByTestId(
      '<EMAIL>'
    );

    userEvent.click(tuckerRemoveButton);

    expect(mockRemoveResources).toHaveBeenCalledWith({
      memberIds: [
        'Folder::d2315ccb-af9a-41c4-ab36-3cbaf61acdd8::91083288-b420-4bf6-9b6b-a9c24c3bda5b::88c05684-33e8-491b-80b3-fdb143ab2b1e',
      ],
      resourceType: 'Folder',
    });

    await waitFor(() => {
      // Tucker should no longer be in the document
      expect(screen.queryByText('Tucker Roth')).not.toBeInTheDocument();
    });
  });

  it('A person can be added to permissioned users', async () => {
    const store = configureAppStore({
      ...initialStateForMock,
      caseManager: {
        ...initialStateForMock.caseManager,
        editingCaseFolderId: 'caseFolderId123',
      },
    });

    const mockAddAcesToResources = vi
      .spyOn(GQLApi.prototype, 'addACEsToResources')
      .mockImplementation(() =>
        Promise.resolve(
          permsissionedCaseFolderDataWithBenHaAdded as {
            records: ACLRecordType[];
          }
        )
      );

    const mockOnClose = vi.fn();

    render(
      <Provider store={store}>
        <PermissionsPanel onClose={mockOnClose} />
      </Provider>
    );

    await waitFor(() => {
      // Tucker should be in the list as they are permission-ed
      expect(screen.getByText('Tucker Roth')).toBeInTheDocument();
    });

    // Search for Ben Ha using the select
    const selectMenu = screen.getByTestId('permission-select-search-input');
    userEvent.type(selectMenu, 'Ben');

    // It should now be one of the options
    await waitFor(() => {
      expect(screen.getByText('Ben Ha')).toBeInTheDocument();
    });

    // Click Ben Ha MenuItem
    fireEvent.click(screen.getByText('Ben Ha'));
    expect(mockAddAcesToResources).toHaveBeenCalled();

    //  The Ben Ha menu item is no longer available because the select menu closed
    const benHaMenuItem = screen.queryByTestId(
      'permission-select-item-ea3d773d-2ab4-4769-a731-e88e3faa4e56'
    );
    expect(benHaMenuItem).toBeNull();

    // But Ben Ha is in the document, because got added as a permission-ed member
    await waitFor(() => {
      expect(screen.getByText('Ben Ha')).toBeInTheDocument();
    });
  });
});
