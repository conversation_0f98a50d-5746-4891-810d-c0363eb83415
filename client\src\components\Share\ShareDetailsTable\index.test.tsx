import { fireEvent, screen, waitFor, cleanup } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { MemoryRouter } from 'react-router';
import { render } from '../../../../test/render';
import ShareFilesTable from './index';
import { mockSharedFiles } from '../mockData';
import { configureAppStore } from '@store/index';
import { Provider } from 'react-redux';
import { SnackbarProvider } from 'notistack';
import { IntlProvider } from 'react-intl';
import translations from '../../../i18n/messages/en-US.json';

window.HTMLElement.prototype.scrollIntoView = vi.fn();

vi.mock('@tanstack/react-virtual', () => ({
  useVirtualizer: vi.fn(() => {
    const virtualItems = mockSharedFiles.map((_, index) => ({
      index,
      size: 69,
      start: index * 69,
      key: index,
      measureElement: vi.fn(),
    }));

    return {
      getVirtualItems: () => virtualItems,
      getTotalSize: () => virtualItems.length * 69,
    };
  }),
}));

vi.mock('@i18n', () => ({
  I18nTranslate: {
    TranslateMessage: (id: string) => id,
    Intl: () => ({
      formatMessage: ({ id }: { id: string }) => id,
    }),
  },
  sagaIntl: () => ({
    formatMessage: ({ id }: { id: string }) => id,
  }),
}));

const mockNavigate = vi.fn();
vi.mock('react-router', async () => {
  const actual = await vi.importActual('react-router');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => ({ shareId: 'share-123' }),
  };
});

const renderComponent = (
  storeSetup?: (store: ReturnType<typeof configureAppStore>) => void
) => {
  const store = configureAppStore();

  if (storeSetup) {
    storeSetup(store);
  }

  const renderedComponent = render(
    <Provider store={store}>
      <SnackbarProvider>
        <IntlProvider locale="en" messages={translations}>
          <MemoryRouter>
            <ShareFilesTable />
          </MemoryRouter>
        </IntlProvider>
      </SnackbarProvider>
    </Provider>
  );
  return { renderedComponent, store };
};

describe('ShareFilesTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    cleanup();
  });

  it('should render the table with initial data correctly', () => {
    renderComponent();

    expect(screen.getByText('caseFile')).toBeInTheDocument();
    expect(screen.getByText('uploadDate')).toBeInTheDocument();
    expect(screen.getByText('fileSize')).toBeInTheDocument();

    const firstFile = mockSharedFiles[0];
    expect(screen.getByText(firstFile.caseFile)).toBeInTheDocument();
    expect(screen.getByText(firstFile.fileSize)).toBeInTheDocument();
  });

  it('should call navigate on row double-click', () => {
    renderComponent((store) => {
      store.dispatch({
        type: 'caseManager/getAllFolderIds/fulfilled',
        payload: {
          validCases: [
            'folder-1',
            'folder-2',
            'folder-3',
            'folder-4',
            'folder-5',
          ],
          searchCases: {
            results: [
              { folderId: 'folder-1', caseId: 'Test Case 1' },
              { folderId: 'folder-2', caseId: 'Test Case 2' },
              { folderId: 'folder-3', caseId: 'Test Case 3' },
              { folderId: 'folder-4', caseId: 'Test Case 4' },
              { folderId: 'folder-5', caseId: 'Test Case 5' },
            ],
          },
        },
      });
    });

    const firstFile = mockSharedFiles[0];
    const firstRow = screen.getByText(firstFile.caseFile);

    fireEvent.doubleClick(firstRow);

    expect(mockNavigate).toHaveBeenCalledTimes(1);
    expect(mockNavigate).toHaveBeenCalledWith(
      `/share-manager/share-123/data-details/${firstFile.id}`
    );
  });

  it('should not call navigate on row double-click for invalid cases', () => {
    renderComponent((store) => {
      store.dispatch({
        type: 'caseManager/getAllFolderIds/fulfilled',
        payload: {
          validCases: [],
          searchCases: {
            results: [
              { folderId: 'folder-1', caseId: 'Test Case 1' },
              { folderId: 'folder-2', caseId: 'Test Case 2' },
              { folderId: 'folder-3', caseId: 'Test Case 3' },
              { folderId: 'folder-4', caseId: 'Test Case 4' },
              { folderId: 'folder-5', caseId: 'Test Case 5' },
            ],
          },
        },
      });
    });

    const firstFile = mockSharedFiles[0];
    const firstRow = screen.getByText(firstFile.caseFile);

    fireEvent.doubleClick(firstRow);

    expect(mockNavigate).toHaveBeenCalledTimes(0);
  });

  it('should dispatch a custom event when a file is selected with Ctrl+Click', async () => {
    const dispatchEventSpy = vi.spyOn(window, 'dispatchEvent');
    renderComponent();

    const firstFile = mockSharedFiles[0];
    const firstRow = screen.getByText(firstFile.caseFile);
    fireEvent.click(firstRow, { ctrlKey: true });
    await waitFor(() => {
      const lastCallIndex = dispatchEventSpy.mock.calls.length - 1;
      const customEvent = dispatchEventSpy.mock.calls[
        lastCallIndex
      ][0] as CustomEvent;

      expect(customEvent.type).toBe('enable-bulk-actions');
      expect(customEvent.detail).toEqual({ isEnabled: true });
    });
  });

  it('should render a clickable download icon for a row', () => {
    renderComponent();

    const downloadButtons = screen.getAllByRole('button', { name: 'download' });
    expect(downloadButtons[0]).toBeInTheDocument();

    fireEvent.click(downloadButtons[0]);
  });
});
